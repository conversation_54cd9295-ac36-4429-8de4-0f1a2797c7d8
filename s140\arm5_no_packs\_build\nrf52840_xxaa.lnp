--cpu=Cortex-M4.fp
".\_build\main.o"
".\_build\boards.o"
".\_build\bsp.o"
".\_build\bsp_btn_ble.o"
".\_build\utf.o"
".\_build\auth_status_tracker.o"
".\_build\ble_advdata.o"
".\_build\ble_advertising.o"
".\_build\ble_conn_params.o"
".\_build\ble_conn_state.o"
".\_build\ble_srv_common.o"
".\_build\gatt_cache_manager.o"
".\_build\gatts_cache_manager.o"
".\_build\id_manager.o"
".\_build\nrf_ble_gatt.o"
".\_build\nrf_ble_qwr.o"
".\_build\peer_data_storage.o"
".\_build\peer_database.o"
".\_build\peer_id.o"
".\_build\peer_manager.o"
".\_build\peer_manager_handler.o"
".\_build\pm_buffer.o"
".\_build\security_dispatcher.o"
".\_build\security_manager.o"
".\_build\ble_link_ctx_manager.o"
".\_build\nrf_drv_clock.o"
".\_build\nrf_drv_uart.o"
".\_build\nrfx_atomic.o"
".\_build\nrfx_clock.o"
".\_build\nrfx_gpiote.o"
".\_build\nrfx_prs.o"
".\_build\nrfx_uart.o"
".\_build\nrfx_uarte.o"
".\_build\nrf_drv_spi.o"
".\_build\nrfx_spim.o"
".\_build\app_button.o"
".\_build\app_error.o"
".\_build\app_error_handler_keil.o"
".\_build\app_error_weak.o"
".\_build\app_scheduler.o"
".\_build\app_timer2.o"
".\_build\app_util_platform.o"
".\_build\crc16.o"
".\_build\drv_rtc.o"
".\_build\fds.o"
".\_build\hardfault_implementation.o"
".\_build\nrf_assert.o"
".\_build\nrf_atfifo.o"
".\_build\nrf_atflags.o"
".\_build\nrf_atomic.o"
".\_build\nrf_balloc.o"
".\_build\nrf_fprintf.o"
".\_build\nrf_fprintf_format.o"
".\_build\nrf_fstorage.o"
".\_build\nrf_fstorage_sd.o"
".\_build\nrf_memobj.o"
".\_build\nrf_pwr_mgmt.o"
".\_build\nrf_ringbuf.o"
".\_build\nrf_section_iter.o"
".\_build\nrf_sortlist.o"
".\_build\nrf_strerror.o"
".\_build\sensorsim.o"
".\_build\app_uart_fifo.o"
".\_build\app_fifo.o"
".\_build\retarget.o"
".\_build\nrf_log_backend_rtt.o"
".\_build\nrf_log_backend_serial.o"
".\_build\nrf_log_backend_uart.o"
".\_build\nrf_log_default_backends.o"
".\_build\nrf_log_frontend.o"
".\_build\nrf_log_str_formatter.o"
".\_build\segger_rtt.o"
".\_build\segger_rtt_syscalls_keil.o"
".\_build\segger_rtt_printf.o"
".\_build\nrf_sdh.o"
".\_build\nrf_sdh_ble.o"
".\_build\nrf_sdh_soc.o"
".\_build\spi.o"
".\_build\st7789.o"
".\_build\key.o"
".\_build\gui.o"
".\_build\events_init.o"
".\_build\gui_guider.o"
".\_build\widgets_init.o"
".\_build\lv_font_montserratmedium_16.o"
".\_build\_baby_alpha_82x47.o"
".\_build\_ysj10_alpha_14x42.o"
".\_build\lv_font_montserratmedium_20.o"
".\_build\_single_alpha_64x64.o"
".\_build\_logo_alpha_95x39.o"
".\_build\setup_scr_average.o"
".\_build\setup_scr_baby2s.o"
".\_build\setup_scr_bluedis.o"
".\_build\setup_scr_bluetoothconnect.o"
".\_build\setup_scr_charged.o"
".\_build\setup_scr_lock.o"
".\_build\setup_scr_log.o"
".\_build\setup_scr_lowbattery.o"
".\_build\setup_scr_result1.o"
".\_build\setup_scr_result1m.o"
".\_build\setup_scr_single.o"
".\_build\lv_font_montserratmedium_17.o"
".\_build\lv_font_montserratmedium_18.o"
".\_build\lv_font_montserratmedium_58.o"
".\_build\_average_alpha_64x64.o"
".\_build\_lowbattery_alpha_64x64.o"
".\_build\_bluetoothconnection_alpha_64x64.o"
".\_build\_charged_alpha_64x64.o"
".\_build\_lock_alpha_64x64.o"
".\_build\_80_alpha_28x18.o"
".\_build\_bluetooth_alpha_18x18.o"
".\_build\_dot1_alpha_12x12.o"
".\_build\_drop_alpha_32x15.o"
".\_build\_one_alpha_18x18.o"
".\_build\_slider_alpha_272x3.o"
".\_build\lv_font_chinese_bold.o"
".\_build\lv_port_disp.o"
".\_build\lv_port_indev.o"
".\_build\lv_disp.o"
".\_build\lv_event.o"
".\_build\lv_group.o"
".\_build\lv_indev.o"
".\_build\lv_indev_scroll.o"
".\_build\lv_obj.o"
".\_build\lv_obj_class.o"
".\_build\lv_obj_draw.o"
".\_build\lv_obj_pos.o"
".\_build\lv_obj_scroll.o"
".\_build\lv_obj_style.o"
".\_build\lv_obj_style_gen.o"
".\_build\lv_obj_tree.o"
".\_build\lv_refr.o"
".\_build\lv_theme.o"
".\_build\lv_gpu_arm2d.o"
".\_build\lv_gpu_nxp_pxp.o"
".\_build\lv_gpu_nxp_pxp_osa.o"
".\_build\lv_gpu_nxp_vglite.o"
".\_build\lv_draw_sdl.o"
".\_build\lv_draw_sdl_arc.o"
".\_build\lv_draw_sdl_bg.o"
".\_build\lv_draw_sdl_composite.o"
".\_build\lv_draw_sdl_img.o"
".\_build\lv_draw_sdl_label.o"
".\_build\lv_draw_sdl_line.o"
".\_build\lv_draw_sdl_mask.o"
".\_build\lv_draw_sdl_polygon.o"
".\_build\lv_draw_sdl_rect.o"
".\_build\lv_draw_sdl_stack_blur.o"
".\_build\lv_draw_sdl_texture_cache.o"
".\_build\lv_draw_sdl_utils.o"
".\_build\lv_gpu_stm32_dma2d.o"
".\_build\lv_draw_sw.o"
".\_build\lv_draw_sw_arc.o"
".\_build\lv_draw_sw_blend.o"
".\_build\lv_draw_sw_dither.o"
".\_build\lv_draw_sw_gradient.o"
".\_build\lv_draw_sw_img.o"
".\_build\lv_draw_sw_letter.o"
".\_build\lv_draw_sw_line.o"
".\_build\lv_draw_sw_polygon.o"
".\_build\lv_draw_sw_rect.o"
".\_build\lv_draw.o"
".\_build\lv_draw_arc.o"
".\_build\lv_draw_img.o"
".\_build\lv_draw_label.o"
".\_build\lv_draw_line.o"
".\_build\lv_draw_mask.o"
".\_build\lv_draw_rect.o"
".\_build\lv_draw_triangle.o"
".\_build\lv_img_buf.o"
".\_build\lv_img_cache.o"
".\_build\lv_img_decoder.o"
".\_build\lv_flex.o"
".\_build\lv_grid.o"
".\_build\lv_bmp.o"
".\_build\lv_ffmpeg.o"
".\_build\lv_freetype.o"
".\_build\lv_fs_fatfs.o"
".\_build\lv_fs_posix.o"
".\_build\lv_fs_stdio.o"
".\_build\lv_fs_win32.o"
".\_build\gifdec.o"
".\_build\lv_gif.o"
".\_build\lodepng.o"
".\_build\lv_png.o"
".\_build\lv_qrcode.o"
".\_build\qrcodegen.o"
".\_build\lv_rlottie.o"
".\_build\lv_sjpg.o"
".\_build\tjpgd.o"
".\_build\lv_fragment.o"
".\_build\lv_fragment_manager.o"
".\_build\lv_gridnav.o"
".\_build\lv_imgfont.o"
".\_build\lv_monkey.o"
".\_build\lv_snapshot.o"
".\_build\lv_theme_basic.o"
".\_build\lv_theme_default.o"
".\_build\lv_theme_mono.o"
".\_build\lv_animimg.o"
".\_build\lv_calendar.o"
".\_build\lv_calendar_header_arrow.o"
".\_build\lv_calendar_header_dropdown.o"
".\_build\lv_chart.o"
".\_build\lv_colorwheel.o"
".\_build\lv_imgbtn.o"
".\_build\lv_keyboard.o"
".\_build\lv_led.o"
".\_build\lv_list.o"
".\_build\lv_menu.o"
".\_build\lv_meter.o"
".\_build\lv_msgbox.o"
".\_build\lv_span.o"
".\_build\lv_spinbox.o"
".\_build\lv_spinner.o"
".\_build\lv_tabview.o"
".\_build\lv_tileview.o"
".\_build\lv_win.o"
".\_build\lv_extra.o"
".\_build\lv_font.o"
".\_build\lv_font_dejavu_16_persian_hebrew.o"
".\_build\lv_font_fmt_txt.o"
".\_build\lv_font_loader.o"
".\_build\lv_font_montserrat_8.o"
".\_build\lv_font_montserrat_10.o"
".\_build\lv_font_montserrat_12.o"
".\_build\lv_font_montserrat_12_subpx.o"
".\_build\lv_font_montserrat_14.o"
".\_build\lv_font_montserrat_16.o"
".\_build\lv_font_montserrat_18.o"
".\_build\lv_font_montserrat_20.o"
".\_build\lv_font_montserrat_22.o"
".\_build\lv_font_montserrat_24.o"
".\_build\lv_font_montserrat_26.o"
".\_build\lv_font_montserrat_28.o"
".\_build\lv_font_montserrat_28_compressed.o"
".\_build\lv_font_montserrat_30.o"
".\_build\lv_font_montserrat_32.o"
".\_build\lv_font_montserrat_34.o"
".\_build\lv_font_montserrat_36.o"
".\_build\lv_font_montserrat_38.o"
".\_build\lv_font_montserrat_40.o"
".\_build\lv_font_montserrat_42.o"
".\_build\lv_font_montserrat_44.o"
".\_build\lv_font_montserrat_46.o"
".\_build\lv_font_montserrat_48.o"
".\_build\lv_font_simsun_16_cjk.o"
".\_build\lv_font_unscii_8.o"
".\_build\lv_font_unscii_16.o"
".\_build\lv_hal_disp.o"
".\_build\lv_hal_indev.o"
".\_build\lv_hal_tick.o"
".\_build\lv_anim.o"
".\_build\lv_anim_timeline.o"
".\_build\lv_area.o"
".\_build\lv_async.o"
".\_build\lv_bidi.o"
".\_build\lv_color.o"
".\_build\lv_fs.o"
".\_build\lv_gc.o"
".\_build\lv_ll.o"
".\_build\lv_log.o"
".\_build\lv_lru.o"
".\_build\lv_math.o"
".\_build\lv_mem.o"
".\_build\lv_printf.o"
".\_build\lv_style.o"
".\_build\lv_style_gen.o"
".\_build\lv_templ.o"
".\_build\lv_timer.o"
".\_build\lv_tlsf.o"
".\_build\lv_txt.o"
".\_build\lv_txt_ap.o"
".\_build\lv_utils.o"
".\_build\lv_arc.o"
".\_build\lv_bar.o"
".\_build\lv_btn.o"
".\_build\lv_btnmatrix.o"
".\_build\lv_canvas.o"
".\_build\lv_checkbox.o"
".\_build\lv_dropdown.o"
".\_build\lv_img.o"
".\_build\lv_label.o"
".\_build\lv_line.o"
".\_build\lv_objx_templ.o"
".\_build\lv_roller.o"
".\_build\lv_slider.o"
".\_build\lv_switch.o"
".\_build\lv_table.o"
".\_build\lv_textarea.o"
".\_build\ble_conn.o"
".\_build\ble_nus.o"
".\_build\ble_bas.o"
".\_build\ble_dis.o"
".\_build\ble_dfu.o"
".\_build\ble_dfu_bonded.o"
".\_build\ble_dfu_unbonded.o"
".\_build\nrf_dfu_svci.o"
".\_build\arm_startup_nrf52840.o"
".\_build\system_nrf52.o"
--library_type=microlib --strict --scatter ".\_build\nrf52840_xxaa.sct"
--diag_suppress 6330 --summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list ".\_build\nrf52840_xxaa.map" -o .\_build\nrf52840_xxaa.axf