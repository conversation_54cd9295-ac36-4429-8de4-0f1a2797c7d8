#include "ble_conn.h"

#include "ble_hci.h"
#include "ble_advdata.h"
#include "ble_advertising.h"
#include "ble_conn_params.h"
#include "ble_nus.h"
#include "ble_dis.h"
#include "ble_bas.h"
#include "ble_dfu.h"
#include "nrf_sdh.h"
#include "nrf_sdh_soc.h"
#include "nrf_sdh_ble.h"
#include "nrf_ble_gatt.h"
#include "nrf_ble_qwr.h"
#include "nrf_power.h"
#include "nrf_bootloader_info.h"
#include "nrf_pwr_mgmt.h"
#include "ble_nus.h"
#include "app_uart.h"
#include "nrf_uart.h"

#include "app_timer.h"

#define DIS_DEVICE_NAME                     "YSJ-10"                      				 	/**< �豸���ƣ��������ڹ㲥�����С� */
#define DIS_MANUFACTURER_NAME               "E3A"                   									/**< ���������ƣ������ݸ��豸��Ϣ���� */
#define DIS_MODEL_NUM               				"YSJ-10"
#define APP_ADV_INTERVAL                300                                     /**< �㲥�������λΪ0.625ms����ֵ��Ӧ187.5ms���� */
#define HW_VER  					"3.0"
#define FW_VER  					"v3.3.4"

#define NUS_SERVICE_UUID_TYPE           BLE_UUID_TYPE_VENDOR_BEGIN                  /**< UUID type for the Nordic UART Service (vendor specific). */

#define APP_ADV_DURATION                18000                                   /**< �㲥����ʱ�䣨180�룩����λΪ10���롣 */
#define APP_BLE_OBSERVER_PRIO           3                                       /**< Ӧ�õ�BLE�۲������ȼ���ͨ�������޸ġ� */
#define APP_BLE_CONN_CFG_TAG            1                                       /**< ��ʶSoftDevice BLE���õı�ǩ�� */

/*lint -emacro(524, MIN_CONN_INTERVAL) // ������ʧ */
#define MIN_CONN_INTERVAL               MSEC_TO_UNITS(20, UNIT_1_25_MS)        /**< ��С�ɽ������Ӽ����7.5���룩�� */
#define MAX_CONN_INTERVAL               MSEC_TO_UNITS(40, UNIT_1_25_MS)        /**< ���ɽ������Ӽ����30���룩�� */
#define SLAVE_LATENCY                   0                                       /**< �ӻ��ӳ١� */
#define CONN_SUP_TIMEOUT                MSEC_TO_UNITS(4000, UNIT_10_MS)         /**< ���Ӽ�س�ʱ��4�룩�� */

#define FIRST_CONN_PARAMS_UPDATE_DELAY  APP_TIMER_TICKS(5000)                   /**< �ӷ����¼������ӻ�֪ͨ��ʼ������һ�ε���sd_ble_gap_conn_param_update��ʱ�䣨5�룩�� */
#define NEXT_CONN_PARAMS_UPDATE_DELAY   APP_TIMER_TICKS(30000)                  /**< ��һ�ε��ú�ÿ�ε���sd_ble_gap_conn_param_update�ļ����30�룩�� */
#define MAX_CONN_PARAMS_UPDATE_COUNT    3                                       /**< �������Ӳ���Э��ǰ������Դ����� */

#define DEAD_BEEF                       0xDEADBEEF                              /**< ��Ϊ��ջת��ʱ�Ĵ����룬�����ڶ�λ��ջλ�á� */

BLE_NUS_DEF(m_nus, NRF_SDH_BLE_TOTAL_LINK_COUNT);                                   /**< BLE NUS service instance. */
NRF_BLE_GATT_DEF(m_gatt);                                                       /**< GATTģ��ʵ���� */
NRF_BLE_QWR_DEF(m_qwr);                                                         /**< ����Queued Writeģ��������ġ�*/
BLE_ADVERTISING_DEF(m_advertising);                                             /**< ���ģ��ʵ���� */
BLE_BAS_DEF(m_bas);                                                                 /**< Structure used to identify the battery service. */

uint16_t m_ble_nus_max_data_len = BLE_GATT_ATT_MTU_DEFAULT - 3;                     /**< Maximum length of data (in bytes) that can be transmitted to the peer by the Nordic UART service module. */

static ble_uuid_t m_adv_uuids[]          =                                          /**< Universally unique service identifier. */
{
    {BLE_UUID_NUS_SERVICE, NUS_SERVICE_UUID_TYPE}
};

static uint16_t m_conn_handle = BLE_CONN_HANDLE_INVALID;                        /**< ��ǰ���ӵľ���� */

static uint32_t m_is_ble_advertising = 0;//�Ƿ�㲥�б�־
char ble_name_buf[50];//���������ַ���



/* ============================================================================================================= */
// Э��ջ��ʼ��
/* ============================================================================================================= */

/**@brief BLE�¼�����������
 *
 * @param[in]   p_ble_evt   ����Э��ջ�¼���
 * @param[in]   p_context   δʹ�á�
 */
static void ble_evt_handler(ble_evt_t const * p_ble_evt, void * p_context)
{
    uint32_t err_code;

    switch (p_ble_evt->header.evt_id)
    {
        case BLE_GAP_EVT_CONNECTED:
            NRF_LOG_INFO("Connected");
            
            m_conn_handle = p_ble_evt->evt.gap_evt.conn_handle;
            err_code = nrf_ble_qwr_conn_handle_assign(&m_qwr, m_conn_handle);
            APP_ERROR_CHECK(err_code);
            
            //�������Ӻ��ӳ��Զ��ػ�ʱ��
//gd				sm_set_auto_power_off_time(AUTO_POWER_OFF_SECOND * 5);  //divid  sm_set_auto_power_off_time(AUTO_POWER_OFF_SECOND );
			//��һ��
//gd			motor_start_once(100);
            break;

        case BLE_GAP_EVT_DISCONNECTED:
            NRF_LOG_INFO("Disconnected");
            // LED indication will be changed when advertising starts.
            m_conn_handle = BLE_CONN_HANDLE_INVALID;
//gd            frame_clear();//���FIFO
            
            //�����Ͽ���ָ��Զ��ػ�ʱ��
//gd            sm_set_auto_power_off_time(AUTO_POWER_OFF_SECOND);
			
			//��ֹ��������Ƥ��
//gd			test_aldult_off();
			
            break;

        case BLE_GAP_EVT_PHY_UPDATE_REQUEST:
        {
            NRF_LOG_DEBUG("PHY update request.");
            ble_gap_phys_t const phys =
            {
                .rx_phys = BLE_GAP_PHY_AUTO,
                .tx_phys = BLE_GAP_PHY_AUTO,
            };
            err_code = sd_ble_gap_phy_update(p_ble_evt->evt.gap_evt.conn_handle, &phys);
            APP_ERROR_CHECK(err_code);
        } break;

        case BLE_GAP_EVT_SEC_PARAMS_REQUEST:
            // Pairing not supported
            err_code = sd_ble_gap_sec_params_reply(m_conn_handle, BLE_GAP_SEC_STATUS_PAIRING_NOT_SUPP, NULL, NULL);
            APP_ERROR_CHECK(err_code);
            break;

        case BLE_GATTS_EVT_SYS_ATTR_MISSING:
            // No system attributes have been stored.
            err_code = sd_ble_gatts_sys_attr_set(m_conn_handle, NULL, 0, 0);
            APP_ERROR_CHECK(err_code);
            break;

        case BLE_GATTC_EVT_TIMEOUT:
            // Disconnect on GATT Client timeout event.
            err_code = sd_ble_gap_disconnect(p_ble_evt->evt.gattc_evt.conn_handle,
                                             BLE_HCI_REMOTE_USER_TERMINATED_CONNECTION);
            APP_ERROR_CHECK(err_code);
            break;

        case BLE_GATTS_EVT_TIMEOUT:
            // Disconnect on GATT Server timeout event.
            err_code = sd_ble_gap_disconnect(p_ble_evt->evt.gatts_evt.conn_handle,
                                             BLE_HCI_REMOTE_USER_TERMINATED_CONNECTION);
            APP_ERROR_CHECK(err_code);
            break;

        default:
            // No implementation needed.
            break;
    }
}
/**@brief BLEЭ��ջ��ʼ��������
 *
 * @details ��ʼ��SoftDevice��BLE�¼��жϡ�
 */
static void ble_stack_init(void)
{
    ret_code_t err_code;

    err_code = nrf_sdh_enable_request();
    APP_ERROR_CHECK(err_code);

    // Configure the BLE stack using the default settings.
    // Fetch the start address of the application RAM.
    uint32_t ram_start = 0;
    err_code = nrf_sdh_ble_default_cfg_set(APP_BLE_CONN_CFG_TAG, &ram_start);
    APP_ERROR_CHECK(err_code);

    // Enable BLE stack.
    err_code = nrf_sdh_ble_enable(&ram_start);
    APP_ERROR_CHECK(err_code);

    // Register a handler for BLE events.
    NRF_SDH_BLE_OBSERVER(m_ble_observer, APP_BLE_OBSERVER_PRIO, ble_evt_handler, NULL);
}


/* ============================================================================================================= */
// GAP������ʼ��
/* ============================================================================================================= */

/**@brief GAP��ʼ��������
 *
 * @details �����豸������GAP��ͨ�÷��������ļ��������������豸������ۺ���ѡ���Ӳ�����
 */
static void gap_params_init(void)
{
    ret_code_t              err_code;
    ble_gap_conn_params_t   gap_conn_params;
    ble_gap_conn_sec_mode_t sec_mode;
		BLE_GAP_CONN_SEC_MODE_SET_OPEN(&sec_mode);
		//my
		ble_gap_addr_t addr;
    err_code = sd_ble_gap_addr_get(&addr);
    APP_ERROR_CHECK(err_code);   
//    NRF_LOG_INFO("BLE addr: %0.2X %0.2X %0.2X %0.2X %0.2X %0.2X", addr.addr[0], addr.addr[1], addr.addr[2], addr.addr[3], addr.addr[4], addr.addr[5]);
		//
		memset(ble_name_buf, 0, sizeof(ble_name_buf));
    snprintf(ble_name_buf, sizeof(ble_name_buf), "%s_%0.5d", DIS_DEVICE_NAME, addr.addr[0] * addr.addr[1]);

    err_code = sd_ble_gap_device_name_set(&sec_mode,
                                          (const uint8_t *)ble_name_buf,
                                          strlen(ble_name_buf));	
    APP_ERROR_CHECK(err_code);


    memset(&gap_conn_params, 0, sizeof(gap_conn_params));

    gap_conn_params.min_conn_interval = MIN_CONN_INTERVAL;
    gap_conn_params.max_conn_interval = MAX_CONN_INTERVAL;
    gap_conn_params.slave_latency     = SLAVE_LATENCY;
    gap_conn_params.conn_sup_timeout  = CONN_SUP_TIMEOUT;

    err_code = sd_ble_gap_ppcp_set(&gap_conn_params);
    APP_ERROR_CHECK(err_code);
}

/* ============================================================================================================= */
// GATT��ʼ��
/* ============================================================================================================= */
/**@brief Function for handling events from the GATT library. */
static void gatt_evt_handler(nrf_ble_gatt_t * p_gatt, nrf_ble_gatt_evt_t const * p_evt)
{
    if ((m_conn_handle == p_evt->conn_handle) && (p_evt->evt_id == NRF_BLE_GATT_EVT_ATT_MTU_UPDATED))
    {
        m_ble_nus_max_data_len = p_evt->params.att_mtu_effective - OPCODE_LENGTH - HANDLE_LENGTH;
        NRF_LOG_INFO("Data len is set to 0x%X(%d)", m_ble_nus_max_data_len, m_ble_nus_max_data_len);
    }
    NRF_LOG_DEBUG("ATT MTU exchange completed. central 0x%x peripheral 0x%x",
                  p_gatt->att_mtu_desired_central,
                  p_gatt->att_mtu_desired_periph);
}



/**@brief GATTģ���ʼ��������
 */
static void gatt_init(void)
{
    ret_code_t err_code;

    err_code = nrf_ble_gatt_init(&m_gatt, gatt_evt_handler);
    APP_ERROR_CHECK(err_code);

    err_code = nrf_ble_gatt_att_mtu_periph_set(&m_gatt, NRF_SDH_BLE_GATT_MAX_MTU_SIZE);
    APP_ERROR_CHECK(err_code);
		
}


/**@brief Function for handling Queued Write Module errors.
 *
 * @details A pointer to this function will be passed to each service which may need to inform the
 *          application about an error.
 *
 * @param[in]   nrf_error   Error code containing information about what went wrong.
 */
static void nrf_qwr_error_handler(uint32_t nrf_error)
{
    APP_ERROR_HANDLER(nrf_error);
}



/**@brief Function for handling the data from the Nordic UART Service.
 *
 * @details This function will process the data received from the Nordic UART BLE Service and send
 *          it to the UART module.
 *
 * @param[in] p_evt       Nordic UART Service event.
 */
/**@snippet [Handling the data received over BLE] */
static void nus_data_handler(ble_nus_evt_t * p_evt)
{
    //���յ�����
    if (p_evt->type == BLE_NUS_EVT_RX_DATA)
    {
			//NRF_LOG_INFO("S1length:%d", p_evt->params.rx_data.length);
//gd       frame_recv_ble_data(p_evt->params.rx_data.p_data, p_evt->params.rx_data.length);
    }
    //���Խ������������ڷ���
    else if (p_evt->type == BLE_NUS_EVT_TX_RDY)
    {
        //������������͵���������Э��ջ
//gd        ble_low_layer_send_data();
    }
    //Notification��ʹ��
    else if (p_evt->type == BLE_NUS_EVT_COMM_STARTED)
    {
        NRF_LOG_INFO("NUS Notification ON");
		//֪ͨAPP����ģʽ����������APP��ʾ
//gd		protocol_ble_send_model(average_times_total);
    }
    //Notification���ر�
    else if (p_evt->type == BLE_NUS_EVT_COMM_STOPPED)
    {
        NRF_LOG_INFO("NUS Notification OFF");
    }
}

/**@snippet [Handling the data received over BLE] */


bool ble_conn_is_connected(void)
{
    if (m_conn_handle != BLE_CONN_HANDLE_INVALID)
    {
        return true;
    }
    else
    {
        return false;
    }
}

//send one byte through NUS
uint32_t nus_send_byte(uint8_t* data, uint16_t len)
{
    ret_code_t err_code;
    
    err_code = ble_nus_data_send(&m_nus, data, &len, m_conn_handle);
    
    return err_code;
}


static void buttonless_dfu_sdh_state_observer(nrf_sdh_state_evt_t state, void * p_context)
{
    if (state == NRF_SDH_EVT_STATE_DISABLED)
    {
        // Softdevice was disabled before going into reset. Inform bootloader to skip CRC on next boot.
        nrf_power_gpregret2_set(BOOTLOADER_DFU_SKIP_CRC);

        //Go to system off.
        nrf_pwr_mgmt_shutdown(NRF_PWR_MGMT_SHUTDOWN_GOTO_SYSOFF);
    }
}


/* nrf_sdh state observer. */
NRF_SDH_STATE_OBSERVER(m_buttonless_dfu_state_obs, 0) =
{
    .handler = buttonless_dfu_sdh_state_observer,
};

/* ============================================================================================================= */
// �㲥��ʼ��
/* ============================================================================================================= */

static void advertising_config_get(ble_adv_modes_config_t * p_config)
{
    memset(p_config, 0, sizeof(ble_adv_modes_config_t));

    p_config->ble_adv_fast_enabled  = true;
    p_config->ble_adv_fast_interval = APP_ADV_INTERVAL;
    p_config->ble_adv_fast_timeout  = APP_ADV_DURATION;
}


static void disconnect(uint16_t conn_handle, void * p_context)
{
    UNUSED_PARAMETER(p_context);

    ret_code_t err_code = sd_ble_gap_disconnect(conn_handle, BLE_HCI_REMOTE_USER_TERMINATED_CONNECTION);
    if (err_code != NRF_SUCCESS)
    {
        NRF_LOG_WARNING("Failed to disconnect connection. Connection handle: %d Error: %d", conn_handle, err_code);
    }
    else
    {
        NRF_LOG_DEBUG("Disconnected connection handle %d", conn_handle);
    }
}

static void on_adv_evt(ble_adv_evt_t ble_adv_evt)
{
    switch (ble_adv_evt)
    {
        case BLE_ADV_EVT_FAST:
            m_is_ble_advertising = 1;
            break;
        case BLE_ADV_EVT_IDLE:
            m_is_ble_advertising = 0;
            break;
        default:
            break;
    }
}

/**@brief Function for handling dfu events from the Buttonless Secure DFU service
 *
 * @param[in]   event   Event from the Buttonless Secure DFU service.
 */
static void ble_dfu_evt_handler(ble_dfu_buttonless_evt_type_t event)
{
    switch (event)
    {
        case BLE_DFU_EVT_BOOTLOADER_ENTER_PREPARE:
        {
            NRF_LOG_INFO("Device is preparing to enter bootloader mode.");

            // Prevent device from advertising on disconnect.
            ble_adv_modes_config_t config;
            advertising_config_get(&config);
            config.ble_adv_on_disconnect_disabled = true;
            ble_advertising_modes_config_set(&m_advertising, &config);

            // Disconnect all other bonded devices that currently are connected.
            // This is required to receive a service changed indication
            // on bootup after a successful (or aborted) Device Firmware Update.
            uint32_t conn_count = ble_conn_state_for_each_connected(disconnect, NULL);
            NRF_LOG_INFO("Disconnected %d links.", conn_count);
            break;
        }

        case BLE_DFU_EVT_BOOTLOADER_ENTER:
            // YOUR_JOB: Write app-specific unwritten data to FLASH, control finalization of this
            //           by delaying reset by reporting false in app_shutdown_handler
            NRF_LOG_INFO("Device will enter bootloader mode.");
            break;

        case BLE_DFU_EVT_BOOTLOADER_ENTER_FAILED:
            NRF_LOG_ERROR("Request to enter bootloader mode failed asynchroneously.");
            // YOUR_JOB: Take corrective measures to resolve the issue
            //           like calling APP_ERROR_CHECK to reset the device.
            break;

        case BLE_DFU_EVT_RESPONSE_SEND_ERROR:
            NRF_LOG_ERROR("Request to send a response to client failed.");
            // YOUR_JOB: Take corrective measures to resolve the issue
            //           like calling APP_ERROR_CHECK to reset the device.
            APP_ERROR_CHECK(false);
            break;

        default:
            NRF_LOG_ERROR("Unknown event from ble_dfu_buttonless.");
            break;
    }
}

/**@brief �㲥���ܳ�ʼ��������
 */
static void advertising_init(void)
{
    uint32_t               err_code;
    ble_advertising_init_t init;

    memset(&init, 0, sizeof(init));

    init.advdata.name_type          = BLE_ADVDATA_FULL_NAME;
    init.advdata.include_appearance = false;
    init.advdata.flags              = BLE_GAP_ADV_FLAGS_LE_ONLY_LIMITED_DISC_MODE;

    init.srdata.uuids_complete.uuid_cnt = sizeof(m_adv_uuids) / sizeof(m_adv_uuids[0]);
    init.srdata.uuids_complete.p_uuids  = m_adv_uuids;

    init.config.ble_adv_on_disconnect_disabled = false;//�Ͽ��������Զ������㲥
    init.config.ble_adv_fast_enabled  = true;
    init.config.ble_adv_fast_interval = APP_ADV_INTERVAL;
    init.config.ble_adv_fast_timeout  = APP_ADV_DURATION;//�㲥ʱ��
    init.evt_handler = on_adv_evt;

    err_code = ble_advertising_init(&m_advertising, &init);
    APP_ERROR_CHECK(err_code);

    ble_advertising_conn_cfg_tag_set(&m_advertising, APP_BLE_CONN_CFG_TAG);
}

/**@brief Function for starting advertising.
 */
void advertising_start(void)
{
    uint32_t err_code = ble_advertising_start(&m_advertising, BLE_ADV_MODE_FAST);
    APP_ERROR_CHECK(err_code);
}


//�رչ㲥
void advertising_stop(void)
{
    if (m_is_ble_advertising)
    {
        (void)sd_ble_gap_adv_stop(m_advertising.adv_handle);
    }
}

/* ============================================================================================================= */
// �����ʼ��
/* ============================================================================================================= */

/**@brief YYY�����¼�����������
 * YOUR_JOB �������÷�����¼�ʵ�ֶ�Ӧ�Ĵ�������
 *
 * @details �˺����ᴦ�����д��ݸ�Ӧ�õ�YY�����¼���
 *
 * @param[in]   p_yy_service   YY����ṹ�塣
 * @param[in]   p_evt          ��YY�����յ����¼���
 *
 *
static void on_yys_evt(ble_yy_service_t     * p_yy_service,
                       ble_yy_service_evt_t * p_evt)
{
    switch (p_evt->evt_type)
    {
        case BLE_YY_NAME_EVT_WRITE:
            APPL_LOG("[APPL]: charact written with value %s. ", p_evt->params.char_xx.value.p_str);
            break;

        default:
            // ����ʵ�֡�
            break;
    }
}
*/


/**@brief Function for initializing services that will be used by the application.
 */
void services_init(void)
{
    uint32_t           err_code;
    ble_nus_init_t     nus_init;
    nrf_ble_qwr_init_t qwr_init = {0};
    ble_dis_init_t     dis_init;
    ble_bas_init_t     bas_init;
    ble_dfu_buttonless_init_t dfus_init = {0};

    printf("services_init start\r\n");

    // Initialize Queued Write Module.
    qwr_init.error_handler = nrf_qwr_error_handler;

    printf("Before nrf_ble_qwr_init\r\n");
    err_code = nrf_ble_qwr_init(&m_qwr, &qwr_init);
    printf("After nrf_ble_qwr_init, err_code=%d\r\n", err_code);
    APP_ERROR_CHECK(err_code);

    // Initialize NUS.
    memset(&nus_init, 0, sizeof(nus_init));

    nus_init.data_handler = nus_data_handler;

    printf("Before ble_nus_init\r\n");

    // 检查内存状态
    printf("About to call ble_nus_init...\r\n");

    // 添加超时保护
    volatile uint32_t timeout = 0;
    const uint32_t MAX_INIT_TIMEOUT = 500000;

    printf("Calling ble_nus_init with timeout protection...\r\n");
    err_code = ble_nus_init(&m_nus, &nus_init);				//������NRF_SDH_BLE_VS_UUID_COUNT 1
    printf("After ble_nus_init, err_code=%d (0x%X)\r\n", err_code, err_code);

    if (err_code != NRF_SUCCESS) {
        printf("ble_nus_init failed with error: 0x%X\r\n", err_code);
        printf("Common error codes:\r\n");
        printf("  NRF_ERROR_NO_MEM (0x4): %s\r\n", (err_code == 0x4) ? "YES - Out of memory!" : "no");
        printf("  NRF_ERROR_INVALID_PARAM (0x7): %s\r\n", (err_code == 0x7) ? "YES - Invalid parameter!" : "no");
        printf("  NRF_ERROR_INVALID_STATE (0x8): %s\r\n", (err_code == 0x8) ? "YES - Invalid state!" : "no");
        // 不要立即调用 APP_ERROR_CHECK，先看看错误码
    }
    APP_ERROR_CHECK(err_code);
    
    // Initialize Device Information Service.
    memset(&dis_init, 0, sizeof(dis_init));
	
	
	//����: 01A22051701-001
	//01A: YSJ-20
	//220517: ��������
	//01: ��������
    //001: ���յĻ������
	char str1[20];
	memset(str1, 0, sizeof(str1));
//gd  storage_read_sn_str((uint8_t*)str1);
	if (strlen(str1) == 0)//�����ûд�����кţ�����������ַ����
	{
		ble_gap_addr_t addr;
		err_code = sd_ble_gap_addr_get(&addr);
		APP_ERROR_CHECK(err_code);
		snprintf(str1, sizeof(str1), "%0.5d", addr.addr[0] * addr.addr[1]);
	}
	
    char str2[20];
    snprintf(str2, sizeof(str2), "HW_V%s", HW_VER);
    
    char str3[30];
    snprintf(str3, sizeof(str3), "%s %s %s", FW_VER, __DATE__, __TIME__);
	
    ble_srv_ascii_to_utf8(&dis_init.manufact_name_str, DIS_MANUFACTURER_NAME);
    ble_srv_ascii_to_utf8(&dis_init.model_num_str, DIS_MODEL_NUM);
    ble_srv_ascii_to_utf8(&dis_init.serial_num_str, str1);
    ble_srv_ascii_to_utf8(&dis_init.hw_rev_str, str2);
    ble_srv_ascii_to_utf8(&dis_init.fw_rev_str, str3);
    dis_init.dis_char_rd_sec = SEC_OPEN;

    err_code = ble_dis_init(&dis_init);
    APP_ERROR_CHECK(err_code);
    
    // Initialize Battery Service.
    memset(&bas_init, 0, sizeof(bas_init));

    // Here the sec level for the Battery Service can be changed/increased.
    bas_init.bl_rd_sec        = SEC_OPEN;
    bas_init.bl_cccd_wr_sec   = SEC_OPEN;
    bas_init.bl_report_rd_sec = SEC_OPEN;

    bas_init.evt_handler          = NULL;
    bas_init.support_notification = true;
    bas_init.p_report_ref         = NULL;
    bas_init.initial_batt_level   = 100;
    
    err_code = ble_bas_init(&m_bas, &bas_init);
    APP_ERROR_CHECK(err_code);
    
    //DFU
    dfus_init.evt_handler = ble_dfu_evt_handler;

		err_code = ble_dfu_buttonless_init(&dfus_init);
    APP_ERROR_CHECK(err_code);
		
}

void ble_battery_level_update(uint32_t battery_level)
{
    APP_ERROR_CHECK_BOOL(battery_level <= 100);
    
    uint32_t err_code;
    
    err_code = ble_bas_battery_level_update(&m_bas, battery_level, BLE_CONN_HANDLE_ALL);
    if ((err_code != NRF_SUCCESS) &&
        (err_code != NRF_ERROR_INVALID_STATE) &&
        (err_code != NRF_ERROR_RESOURCES) &&
        (err_code != BLE_ERROR_GATTS_SYS_ATTR_MISSING))
    {
        APP_ERROR_HANDLER(err_code);
    }
}




/* ============================================================================================================= */
// ���Ӳ�����ʼ��
/* ============================================================================================================= */

/**@brief ���Ӳ���ģ���¼�����������
 *
 * @details �˺����ᴦ�����д��ݸ�Ӧ�õ����Ӳ���ģ���¼���
 *          @note �˺��������ڶϿ����ӡ�Ҳ����ͨ������disconnect_on_fail���ò���ʵ�֣����������¼�������������ʾ��
 *
 * @param[in] p_evt  �����Ӳ���ģ���յ����¼���
 */
static void on_conn_params_evt(ble_conn_params_evt_t * p_evt)
{
    uint32_t err_code;

    if (p_evt->evt_type == BLE_CONN_PARAMS_EVT_FAILED)
    {
        err_code = sd_ble_gap_disconnect(m_conn_handle, BLE_HCI_CONN_INTERVAL_UNACCEPTABLE);
        APP_ERROR_CHECK(err_code);
    }
}


/**@brief ���Ӳ���������������
 *
 * @param[in] nrf_error  �����룬����������Ϣ��
 */
static void conn_params_error_handler(uint32_t nrf_error)
{
    APP_ERROR_HANDLER(nrf_error);
}


/**@brief ���Ӳ���ģ���ʼ��������
 */
static void conn_params_init(void)
{
    ret_code_t             err_code;
    ble_conn_params_init_t cp_init;

    memset(&cp_init, 0, sizeof(cp_init));

    cp_init.p_conn_params                  = NULL;
    cp_init.first_conn_params_update_delay = FIRST_CONN_PARAMS_UPDATE_DELAY;
    cp_init.next_conn_params_update_delay  = NEXT_CONN_PARAMS_UPDATE_DELAY;
    cp_init.max_conn_params_update_count   = MAX_CONN_PARAMS_UPDATE_COUNT;
    cp_init.start_on_notify_cccd_handle    = BLE_GATT_HANDLE_INVALID;
    cp_init.disconnect_on_fail             = false;
    cp_init.evt_handler                    = on_conn_params_evt;
    cp_init.error_handler                  = conn_params_error_handler;

    err_code = ble_conn_params_init(&cp_init);
    APP_ERROR_CHECK(err_code);
}


//�ж��Ƿ�����BLE
bool is_ble_connected(void)
{
    if (m_conn_handle == BLE_CONN_HANDLE_INVALID)
    {
        return false;
    }
    else
    {
        return true;
    }
}

//�Ͽ�BLE����
void disconnect_BLE(void)
{
    if (m_conn_handle != BLE_CONN_HANDLE_INVALID)
    {
        ret_code_t err_code = sd_ble_gap_disconnect(m_conn_handle, BLE_HCI_REMOTE_USER_TERMINATED_CONNECTION);
        if (err_code != NRF_SUCCESS)
        {
            NRF_LOG_WARNING("Failed to disconnect connection. Connection handle: %d Error: %d", m_conn_handle, err_code);
        }
        else
        {
            NRF_LOG_DEBUG("Disconnected connection handle %d", m_conn_handle);
        }
    }
}

/* ============================================================================================================= */
// BLE�ӿں���
/* ============================================================================================================= */

// BLE��ʼ��
void comm_ble_init(void)
{
	  // ��ʼ��BLEЭ��ջ
    ble_stack_init();
		// ��ʼ��GAP���������豸�������Ӳ����ȣ�
    gap_params_init();
    // ��ʼ��GATTģ��
    gatt_init();
	  // ��ʼ���Զ���ͱ�׼BLE����
    services_init();
    // ��ʼ���㲥����
    advertising_init();
    // ��ʼ�����Ӳ���ģ��
    conn_params_init();
		// �����㲥�������Ƿ���Ҫ�������Ϣ
    advertising_start();
}

/**@brief   ���� app_uart �¼��ĺ�����
 *
 * @details �˺������� app_uart ģ����յ����ַ������丽�ӵ�
 *          �ַ����С������յ������һ���ַ��ǻ��з� '\n' (ʮ������ 0x0A)
 *          ���ַ����ﵽ������ݳ���ʱ���ַ�����ͨ�� BLE ���͡�
 */
/**@snippet [����ͨ�� UART ���յ�����] */
void uart_event_handle(app_uart_evt_t * p_event)
{
    static uint8_t data_array[BLE_NUS_MAX_DATA_LEN];
    static uint8_t index = 0;
    uint32_t       err_code;

    switch (p_event->evt_type)
    {
        case APP_UART_DATA_READY:
            UNUSED_VARIABLE(app_uart_get(&data_array[index]));
            index++;

            if ((data_array[index - 1] == '\n') ||
                (data_array[index - 1] == '\r') ||
                (index >= m_ble_nus_max_data_len))
            {
                if (index > 1)
                {
                    NRF_LOG_DEBUG("׼��ͨ�� BLE NUS ��������");
                    NRF_LOG_HEXDUMP_DEBUG(data_array, index);

                    do
                    {
                        uint16_t length = (uint16_t)index;
                        err_code = ble_nus_data_send(&m_nus, data_array, &length, m_conn_handle);
                        if ((err_code != NRF_ERROR_INVALID_STATE) &&
                            (err_code != NRF_ERROR_RESOURCES) &&
                            (err_code != NRF_ERROR_NOT_FOUND))
                        {
                            APP_ERROR_CHECK(err_code);
                        }
                    } while (err_code == NRF_ERROR_RESOURCES);
                }

                index = 0;
            }
            break;

        case APP_UART_COMMUNICATION_ERROR:
            APP_ERROR_HANDLER(p_event->data.error_communication);
            break;

        case APP_UART_FIFO_ERROR:
            APP_ERROR_HANDLER(p_event->data.error_code);
            break;

        default:
            break;
    }
}
/**@snippet [����ͨ�� UART ���յ�����] */