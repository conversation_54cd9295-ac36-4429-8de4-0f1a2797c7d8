.\_build\nrf_drv_clock.o: ..\..\..\..\..\..\integration\nrfx\legacy\nrf_drv_clock.c
.\_build\nrf_drv_clock.o: ..\..\..\..\..\..\components\libraries\util\nordic_common.h
.\_build\nrf_drv_clock.o: ..\..\..\..\..\..\integration\nrfx\legacy\nrf_drv_clock.h
.\_build\nrf_drv_clock.o: ..\..\..\..\..\..\modules\nrfx\drivers\include\nrfx_clock.h
.\_build\nrf_drv_clock.o: ..\..\..\..\..\..\modules\nrfx\nrfx.h
.\_build\nrf_drv_clock.o: ..\..\..\..\..\..\integration\nrfx\nrfx_config.h
.\_build\nrf_drv_clock.o: ..\config\sdk_config.h
.\_build\nrf_drv_clock.o: ..\..\..\..\..\..\modules\nrfx\drivers/nrfx_common.h
.\_build\nrf_drv_clock.o: D:\Keil_mdk\ARM\ARMCC\Bin\..\include\stdint.h
.\_build\nrf_drv_clock.o: D:\Keil_mdk\ARM\ARMCC\Bin\..\include\stddef.h
.\_build\nrf_drv_clock.o: D:\Keil_mdk\ARM\ARMCC\Bin\..\include\stdbool.h
.\_build\nrf_drv_clock.o: D:\Keil_mdk\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.40.3\Device\Include\nrf.h
.\_build\nrf_drv_clock.o: D:\Keil_mdk\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.40.3\Device\Include\nrf52840.h
.\_build\nrf_drv_clock.o: D:\Keil_mdk\ARM\PACK\ARM\CMSIS\5.6.0\CMSIS\Core\Include\core_cm4.h
.\_build\nrf_drv_clock.o: D:\Keil_mdk\ARM\PACK\ARM\CMSIS\5.6.0\CMSIS\Core\Include\cmsis_version.h
.\_build\nrf_drv_clock.o: D:\Keil_mdk\ARM\PACK\ARM\CMSIS\5.6.0\CMSIS\Core\Include\cmsis_compiler.h
.\_build\nrf_drv_clock.o: D:\Keil_mdk\ARM\PACK\ARM\CMSIS\5.6.0\CMSIS\Core\Include\cmsis_armcc.h
.\_build\nrf_drv_clock.o: D:\Keil_mdk\ARM\PACK\ARM\CMSIS\5.6.0\CMSIS\Core\Include\mpu_armv7.h
.\_build\nrf_drv_clock.o: D:\Keil_mdk\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.40.3\Device\Include\system_nrf52840.h
.\_build\nrf_drv_clock.o: D:\Keil_mdk\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.40.3\Device\Include\nrf52840_bitfields.h
.\_build\nrf_drv_clock.o: D:\Keil_mdk\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.40.3\Device\Include\nrf51_to_nrf52840.h
.\_build\nrf_drv_clock.o: D:\Keil_mdk\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.40.3\Device\Include\nrf52_to_nrf52840.h
.\_build\nrf_drv_clock.o: D:\Keil_mdk\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.40.3\Device\Include\compiler_abstraction.h
.\_build\nrf_drv_clock.o: D:\Keil_mdk\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.40.3\Device\Include\nrf_peripherals.h
.\_build\nrf_drv_clock.o: D:\Keil_mdk\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.40.3\Device\Include\nrf52840_peripherals.h
.\_build\nrf_drv_clock.o: ..\..\..\..\..\..\integration\nrfx\nrfx_glue.h
.\_build\nrf_drv_clock.o: ..\..\..\..\..\..\integration\nrfx\legacy/apply_old_config.h
.\_build\nrf_drv_clock.o: ..\..\..\..\..\..\modules\nrfx\soc/nrfx_irqs.h
.\_build\nrf_drv_clock.o: ..\..\..\..\..\..\modules\nrfx\soc/nrfx_irqs_nrf52840.h
.\_build\nrf_drv_clock.o: ..\..\..\..\..\..\components\libraries\util\nrf_assert.h
.\_build\nrf_drv_clock.o: ..\..\..\..\..\..\components\libraries\util\app_util.h
.\_build\nrf_drv_clock.o: ..\..\..\..\..\..\components\softdevice\s140\headers\nrf52\nrf_mbr.h
.\_build\nrf_drv_clock.o: ..\..\..\..\..\..\components\softdevice\s140\headers\nrf_svc.h
.\_build\nrf_drv_clock.o: ..\..\..\..\..\..\components\libraries\util\app_util_platform.h
.\_build\nrf_drv_clock.o: ..\..\..\..\..\..\components\softdevice\s140\headers\nrf_soc.h
.\_build\nrf_drv_clock.o: ..\..\..\..\..\..\components\softdevice\s140\headers\nrf_error.h
.\_build\nrf_drv_clock.o: ..\..\..\..\..\..\components\softdevice\s140\headers\nrf_error_soc.h
.\_build\nrf_drv_clock.o: ..\..\..\..\..\..\components\softdevice\s140\headers\nrf_nvic.h
.\_build\nrf_drv_clock.o: ..\..\..\..\..\..\components\libraries\util\app_error.h
.\_build\nrf_drv_clock.o: D:\Keil_mdk\ARM\ARMCC\Bin\..\include\stdio.h
.\_build\nrf_drv_clock.o: ..\..\..\..\..\..\components\libraries\util\sdk_errors.h
.\_build\nrf_drv_clock.o: ..\..\..\..\..\..\components\libraries\util\app_error_weak.h
.\_build\nrf_drv_clock.o: ..\..\..\..\..\..\modules\nrfx\soc/nrfx_coredep.h
.\_build\nrf_drv_clock.o: ..\..\..\..\..\..\modules\nrfx\soc/nrfx_atomic.h
.\_build\nrf_drv_clock.o: ..\..\..\..\..\..\modules\nrfx\nrfx.h
.\_build\nrf_drv_clock.o: ..\..\..\..\..\..\components\libraries\util\sdk_resources.h
.\_build\nrf_drv_clock.o: ..\..\..\..\..\..\components\softdevice\s140\headers\nrf_sd_def.h
.\_build\nrf_drv_clock.o: ..\..\..\..\..\..\modules\nrfx\drivers/nrfx_errors.h
.\_build\nrf_drv_clock.o: ..\..\..\..\..\..\modules\nrfx\hal/nrf_clock.h
.\_build\nrf_drv_clock.o: ..\..\..\..\..\..\modules\nrfx\drivers\include\nrfx_power_clock.h
.\_build\nrf_drv_clock.o: ..\..\..\..\..\..\components\softdevice\common\nrf_sdh.h
.\_build\nrf_drv_clock.o: ..\..\..\..\..\..\components\libraries\experimental_section_vars\nrf_section_iter.h
.\_build\nrf_drv_clock.o: ..\..\..\..\..\..\components\libraries\experimental_section_vars\nrf_section.h
.\_build\nrf_drv_clock.o: ..\..\..\..\..\..\components\softdevice\common\nrf_sdh_soc.h
.\_build\nrf_drv_clock.o: ..\..\..\..\..\..\components\libraries\util\sdk_common.h
.\_build\nrf_drv_clock.o: D:\Keil_mdk\ARM\ARMCC\Bin\..\include\string.h
.\_build\nrf_drv_clock.o: ..\..\..\..\..\..\components\libraries\util\sdk_os.h
.\_build\nrf_drv_clock.o: ..\..\..\..\..\..\components\libraries\util\sdk_macros.h
.\_build\nrf_drv_clock.o: ..\..\..\..\..\..\modules\nrfx\hal/nrf_wdt.h
.\_build\nrf_drv_clock.o: ..\..\..\..\..\..\components\libraries\log\nrf_log.h
.\_build\nrf_drv_clock.o: ..\..\..\..\..\..\components\libraries\strerror\nrf_strerror.h
.\_build\nrf_drv_clock.o: ..\..\..\..\..\..\components\libraries\log\src\nrf_log_internal.h
.\_build\nrf_drv_clock.o: ..\..\..\..\..\..\components\libraries\log\nrf_log_instance.h
.\_build\nrf_drv_clock.o: ..\..\..\..\..\..\components\libraries\log\nrf_log_types.h
