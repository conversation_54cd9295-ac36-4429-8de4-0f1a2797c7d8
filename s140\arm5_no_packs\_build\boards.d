.\_build\boards.o: ..\..\..\..\..\..\components\boards\boards.c
.\_build\boards.o: ..\..\..\..\..\..\components\boards\boards.h
.\_build\boards.o: ..\..\..\..\..\..\modules\nrfx\hal\nrf_gpio.h
.\_build\boards.o: ..\..\..\..\..\..\modules\nrfx\nrfx.h
.\_build\boards.o: ..\..\..\..\..\..\integration\nrfx\nrfx_config.h
.\_build\boards.o: ..\config\sdk_config.h
.\_build\boards.o: ..\..\..\..\..\..\modules\nrfx\drivers/nrfx_common.h
.\_build\boards.o: D:\Keil_mdk\ARM\ARMCC\Bin\..\include\stdint.h
.\_build\boards.o: D:\Keil_mdk\ARM\ARMCC\Bin\..\include\stddef.h
.\_build\boards.o: D:\Keil_mdk\ARM\ARMCC\Bin\..\include\stdbool.h
.\_build\boards.o: D:\Keil_mdk\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.40.3\Device\Include\nrf.h
.\_build\boards.o: D:\Keil_mdk\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.40.3\Device\Include\nrf52840.h
.\_build\boards.o: D:\Keil_mdk\ARM\PACK\ARM\CMSIS\5.6.0\CMSIS\Core\Include\core_cm4.h
.\_build\boards.o: D:\Keil_mdk\ARM\PACK\ARM\CMSIS\5.6.0\CMSIS\Core\Include\cmsis_version.h
.\_build\boards.o: D:\Keil_mdk\ARM\PACK\ARM\CMSIS\5.6.0\CMSIS\Core\Include\cmsis_compiler.h
.\_build\boards.o: D:\Keil_mdk\ARM\PACK\ARM\CMSIS\5.6.0\CMSIS\Core\Include\cmsis_armcc.h
.\_build\boards.o: D:\Keil_mdk\ARM\PACK\ARM\CMSIS\5.6.0\CMSIS\Core\Include\mpu_armv7.h
.\_build\boards.o: D:\Keil_mdk\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.40.3\Device\Include\system_nrf52840.h
.\_build\boards.o: D:\Keil_mdk\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.40.3\Device\Include\nrf52840_bitfields.h
.\_build\boards.o: D:\Keil_mdk\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.40.3\Device\Include\nrf51_to_nrf52840.h
.\_build\boards.o: D:\Keil_mdk\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.40.3\Device\Include\nrf52_to_nrf52840.h
.\_build\boards.o: D:\Keil_mdk\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.40.3\Device\Include\compiler_abstraction.h
.\_build\boards.o: D:\Keil_mdk\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.40.3\Device\Include\nrf_peripherals.h
.\_build\boards.o: D:\Keil_mdk\ARM\PACK\NordicSemiconductor\nRF_DeviceFamilyPack\8.40.3\Device\Include\nrf52840_peripherals.h
.\_build\boards.o: ..\..\..\..\..\..\integration\nrfx\nrfx_glue.h
.\_build\boards.o: ..\..\..\..\..\..\integration\nrfx\legacy/apply_old_config.h
.\_build\boards.o: ..\..\..\..\..\..\modules\nrfx\soc/nrfx_irqs.h
.\_build\boards.o: ..\..\..\..\..\..\modules\nrfx\soc/nrfx_irqs_nrf52840.h
.\_build\boards.o: ..\..\..\..\..\..\components\libraries\util\nrf_assert.h
.\_build\boards.o: ..\..\..\..\..\..\components\libraries\util\app_util.h
.\_build\boards.o: ..\..\..\..\..\..\components\libraries\util\nordic_common.h
.\_build\boards.o: ..\..\..\..\..\..\components\softdevice\s140\headers\nrf52\nrf_mbr.h
.\_build\boards.o: ..\..\..\..\..\..\components\softdevice\s140\headers\nrf_svc.h
.\_build\boards.o: ..\..\..\..\..\..\components\libraries\util\app_util_platform.h
.\_build\boards.o: ..\..\..\..\..\..\components\softdevice\s140\headers\nrf_soc.h
.\_build\boards.o: ..\..\..\..\..\..\components\softdevice\s140\headers\nrf_error.h
.\_build\boards.o: ..\..\..\..\..\..\components\softdevice\s140\headers\nrf_error_soc.h
.\_build\boards.o: ..\..\..\..\..\..\components\softdevice\s140\headers\nrf_nvic.h
.\_build\boards.o: ..\..\..\..\..\..\components\libraries\util\app_error.h
.\_build\boards.o: D:\Keil_mdk\ARM\ARMCC\Bin\..\include\stdio.h
.\_build\boards.o: ..\..\..\..\..\..\components\libraries\util\sdk_errors.h
.\_build\boards.o: ..\..\..\..\..\..\components\libraries\util\app_error_weak.h
.\_build\boards.o: ..\..\..\..\..\..\modules\nrfx\soc/nrfx_coredep.h
.\_build\boards.o: ..\..\..\..\..\..\modules\nrfx\soc/nrfx_atomic.h
.\_build\boards.o: ..\..\..\..\..\..\modules\nrfx\nrfx.h
.\_build\boards.o: ..\..\..\..\..\..\components\libraries\util\sdk_resources.h
.\_build\boards.o: ..\..\..\..\..\..\components\softdevice\s140\headers\nrf_sd_def.h
.\_build\boards.o: ..\..\..\..\..\..\modules\nrfx\drivers/nrfx_errors.h
.\_build\boards.o: ..\..\..\..\..\..\components\boards\pca10056.h
