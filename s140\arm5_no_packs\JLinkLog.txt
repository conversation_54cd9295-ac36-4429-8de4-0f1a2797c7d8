T3964 4725:068.095   SEGGER J-Link V7.94i Log File
T3964 4725:068.190   DLL Compiled: Feb  7 2024 17:04:01
T3964 4725:068.213   Logging started @ 2025-06-16 08:31
T3964 4725:068.234   Process: D:\Keil_mdk\UV4\UV4.exe
T3964 4725:068.256 - 558.378ms
T3964 4725:068.285 JLINK_SetWarnOutHandler(...)
T3964 4725:068.305 - 0.020ms
T3964 4725:068.330 JLINK_OpenEx(...)
T3964 4725:071.965   Firmware: J-Link V9 compiled May  7 2021 16:26:12
T3964 4725:073.772   Firmware: J-Link V9 compiled May  7 2021 16:26:12
T3964 4725:074.010   Decompressing FW timestamp took 194 us
T3964 4725:082.952   Hardware: V9.40
T3964 4725:083.041   S/N: 69409381
T3964 4725:083.081   OEM: SEGGER
T3964 4725:083.125   Feature(s): <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, JF<PERSON>
T3964 4725:085.139   Bootloader: (Could not read)
T3964 4725:087.257   TELNET listener socket opened on port 19021
T3964 4725:099.929   WEBSRV WEBSRV_Init(): Starting webserver thread(s)
T3964 4725:100.362   WEBSRV Webserver running on local port 19080
T3964 4725:100.640   Looking for J-Link GUI Server exe at: D:\Keil_mdk\ARM\Segger\JLinkGUIServer.exe
T3964 4725:100.709   Looking for J-Link GUI Server exe at: D:\jlink\JLink_V794i\JLinkGUIServer.exe
T3964 4725:100.750   Forking J-Link GUI Server: D:\jlink\JLink_V794i\JLinkGUIServer.exe
T3964 4725:105.144   J-Link GUI Server info: "J-Link GUI server V7.94i "
T3964 4725:106.061 - 37.728ms returns "O.K."
T3964 4725:106.214 JLINK_GetEmuCaps()
T3964 4725:106.238 - 0.022ms returns 0xB9FF7BBF
T3964 4725:106.264 JLINK_TIF_GetAvailable(...)
T3964 4725:106.759 - 0.493ms
T3964 4725:106.825 JLINK_SetErrorOutHandler(...)
T3964 4725:106.844 - 0.019ms
T3964 4725:107.539 JLINK_ExecCommand("ProjectFile = "C:\Users\<USER>\Desktop\project\nRF5_SDK_17.1.0_ddde560\nRF5_SDK_17.1.0_ddde560\examples\ble_peripheral\ble_app_template\pca10056\s140\arm5_no_packs\JLinkSettings.ini"", ...). 
T3964 4725:111.450 - 3.909ms returns 0x00
T3964 4725:111.519 JLINK_ExecCommand("Device = nRF52840_xxAA", ...). 
T3964 4725:111.923   Device "NRF52840_XXAA" selected.
T3964 4725:112.316 - 0.780ms returns 0x00
T3964 4725:112.338 JLINK_ExecCommand("DisableConnectionTimeout", ...). 
T3964 4725:112.352 - 0.001ms returns 0x01
T3964 4725:112.372 JLINK_GetHardwareVersion()
T3964 4725:112.383 - 0.011ms returns 94000
T3964 4725:112.396 JLINK_GetDLLVersion()
T3964 4725:112.407 - 0.010ms returns 79409
T3964 4725:112.420 JLINK_GetOEMString(...)
T3964 4725:112.432 JLINK_GetFirmwareString(...)
T3964 4725:112.446 - 0.014ms
T3964 4725:112.478 JLINK_GetDLLVersion()
T3964 4725:112.490 - 0.011ms returns 79409
T3964 4725:112.501 JLINK_GetCompileDateTime()
T3964 4725:112.513 - 0.011ms
T3964 4725:112.529 JLINK_GetFirmwareString(...)
T3964 4725:112.540 - 0.011ms
T3964 4725:112.554 JLINK_GetHardwareVersion()
T3964 4725:112.565 - 0.010ms returns 94000
T3964 4725:112.580 JLINK_GetSN()
T3964 4725:112.592 - 0.011ms returns 69409381
T3964 4725:112.605 JLINK_GetOEMString(...)
T3964 4725:112.624 JLINK_TIF_Select(JLINKARM_TIF_SWD)
T3964 4725:114.616 - 1.990ms returns 0x00
T3964 4725:114.668 JLINK_HasError()
T3964 4725:114.691 JLINK_SetSpeed(1000)
T3964 4725:115.050 - 0.359ms
T3964 4725:115.069 JLINK_GetId()
T3964 4725:115.615   InitTarget() start
T3964 4725:115.641    J-Link Script File: Executing InitTarget()
T3964 4725:121.245   InitTarget() end - Took 5.57ms
T3964 4725:122.646   Found SW-DP with ID 0x2BA01477
T3964 4725:127.417   DPIDR: 0x2BA01477
T3964 4725:127.445   CoreSight SoC-400 or earlier
T3964 4725:127.468   Scanning AP map to find all available APs
T3964 4725:129.804   AP[2]: Stopped AP scan as end of AP map has been reached
T3964 4725:129.842   AP[0]: AHB-AP (IDR: 0x24770011)
T3964 4725:129.859   AP[1]: JTAG-AP (IDR: 0x02880000)
T3964 4725:129.876   Iterating through AP map to find AHB-AP to use
T3964 4725:132.122   AP[0]: Core found
T3964 4725:132.152   AP[0]: AHB-AP ROM base: 0xE00FF000
T3964 4725:133.251   CPUID register: 0x410FC241. Implementer code: 0x41 (ARM)
T3964 4725:133.281   Found Cortex-M4 r0p1, Little endian.
T3964 4725:134.269   -- Max. mem block: 0x00010C40
T3964 4725:135.286   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T3964 4725:136.288   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T3964 4725:137.243   CPU_ReadMem(4 bytes @ 0x********)
T3964 4725:138.309   FPUnit: 6 code (BP) slots and 2 literal slots
T3964 4725:138.347   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T3964 4725:139.310   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T3964 4725:140.382   CPU_ReadMem(4 bytes @ 0xE0001000)
T3964 4725:141.380   CPU_WriteMem(4 bytes @ 0xE0001000)
T3964 4725:142.465   CPU_ReadMem(4 bytes @ 0xE000ED88)
T3964 4725:143.512   CPU_WriteMem(4 bytes @ 0xE000ED88)
T3964 4725:144.664   CPU_ReadMem(4 bytes @ 0xE000ED88)
T3964 4725:145.748   CPU_WriteMem(4 bytes @ 0xE000ED88)
T3964 4725:146.829   CoreSight components:
T3964 4725:146.877   ROMTbl[0] @ E00FF000
T3964 4725:146.915   CPU_ReadMem(64 bytes @ 0xE00FF000)
T3964 4725:149.016   CPU_ReadMem(32 bytes @ 0xE000EFE0)
T3964 4725:150.577   [0][0]: E000E000 CID B105E00D PID 000BB00C SCS-M7
T3964 4725:150.618   CPU_ReadMem(32 bytes @ 0xE0001FE0)
T3964 4725:152.127   [0][1]: E0001000 CID B105E00D PID 003BB002 DWT
T3964 4725:152.172   CPU_ReadMem(32 bytes @ 0xE0002FE0)
T3964 4725:153.711   [0][2]: ******** CID B105E00D PID 002BB003 FPB
T3964 4725:153.759   CPU_ReadMem(32 bytes @ 0xE0000FE0)
T3964 4725:155.294   [0][3]: ******** CID B105E00D PID 003BB001 ITM
T3964 4725:155.341   CPU_ReadMem(32 bytes @ 0xE0040FE0)
T3964 4725:156.938   [0][4]: ******** CID B105900D PID 000BB9A1 TPIU
T3964 4725:156.986   CPU_ReadMem(32 bytes @ 0xE0041FE0)
T3964 4725:158.496   [0][5]: ******** CID B105900D PID 000BB925 ETM
T3964 4725:159.073 - 44.003ms returns 0x2BA01477
T3964 4725:159.124 JLINK_GetDLLVersion()
T3964 4725:159.150 - 0.025ms returns 79409
T3964 4725:159.259 JLINK_CORE_GetFound()
T3964 4725:159.290 - 0.030ms returns 0xE0000FF
T3964 4725:159.322 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
T3964 4725:159.347   Value=0xE00FF000
T3964 4725:159.390 - 0.068ms returns 0
T3964 4725:159.433 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
T3964 4725:159.458   Value=0xE00FF000
T3964 4725:159.495 - 0.062ms returns 0
T3964 4725:159.522 JLINK_GetDebugInfo(0x101 = JLINKARM_DEBUG_INFO_ETM_ADDR_INDEX)
T3964 4725:159.551   Value=0x********
T3964 4725:159.594 - 0.072ms returns 0
T3964 4725:159.624 JLINK_ReadMemEx(0xE0041FD0, 0x20 Bytes, Flags = 0x02000004)
T3964 4725:159.661   CPU_ReadMem(32 bytes @ 0xE0041FD0)
T3964 4725:161.193   Data:  04 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
T3964 4725:161.250 - 1.625ms returns 32 (0x20)
T3964 4725:161.284 JLINK_GetDebugInfo(0x102 = JLINKARM_DEBUG_INFO_MTB_ADDR_INDEX)
T3964 4725:161.311   Value=0x00000000
T3964 4725:161.348 - 0.065ms returns 0
T3964 4725:161.377 JLINK_GetDebugInfo(0x103 = JLINKARM_DEBUG_INFO_TPIU_ADDR_INDEX)
T3964 4725:161.406   Value=0x********
T3964 4725:161.449 - 0.072ms returns 0
T3964 4725:161.477 JLINK_GetDebugInfo(0x104 = JLINKARM_DEBUG_INFO_ITM_ADDR_INDEX)
T3964 4725:161.502   Value=0x********
T3964 4725:161.538 - 0.060ms returns 0
T3964 4725:161.565 JLINK_GetDebugInfo(0x105 = JLINKARM_DEBUG_INFO_DWT_ADDR_INDEX)
T3964 4725:161.590   Value=0xE0001000
T3964 4725:161.626 - 0.061ms returns 0
T3964 4725:161.651 JLINK_GetDebugInfo(0x106 = JLINKARM_DEBUG_INFO_FPB_ADDR_INDEX)
T3964 4725:161.677   Value=0x********
T3964 4725:161.719 - 0.068ms returns 0
T3964 4725:161.749 JLINK_GetDebugInfo(0x107 = JLINKARM_DEBUG_INFO_NVIC_ADDR_INDEX)
T3964 4725:161.777   Value=0xE000E000
T3964 4725:161.815 - 0.066ms returns 0
T3964 4725:161.843 JLINK_GetDebugInfo(0x10C = JLINKARM_DEBUG_INFO_DBG_ADDR_INDEX)
T3964 4725:161.867   Value=0xE000EDF0
T3964 4725:161.905 - 0.062ms returns 0
T3964 4725:161.940 JLINK_ReadMemU32(0xE000EF40, 0x1 Items)
T3964 4725:161.973   CPU_ReadMem(4 bytes @ 0xE000EF40)
T3964 4725:163.013   Data:  21 00 11 10
T3964 4725:163.079 - 1.139ms returns 1 (0x1)
T3964 4725:163.114 JLINK_ReadMemU32(0xE000EF44, 0x1 Items)
T3964 4725:163.152   CPU_ReadMem(4 bytes @ 0xE000EF44)
T3964 4725:164.171   Data:  11 00 00 11
T3964 4725:164.213 - 1.099ms returns 1 (0x1)
T3964 4725:164.257 JLINK_ReadMemU32(0xE000ED00, 0x1 Items)
T3964 4725:164.285   CPU_ReadMem(4 bytes @ 0xE000ED00)
T3964 4725:165.223   Data:  41 C2 0F 41
T3964 4725:165.263   Debug reg: CPUID
T3964 4725:165.300 - 1.042ms returns 1 (0x1)
T3964 4725:165.330 JLINK_GetDebugInfo(0x10F = JLINKARM_DEBUG_INFO_HAS_CORTEX_M_SECURITY_EXT_INDEX)
T3964 4725:165.356   Value=0x00000000
T3964 4725:165.399 - 0.069ms returns 0
T3964 4725:165.434 JLINK_HasError()
T3964 4725:165.472 JLINK_SetResetType(JLINKARM_CM3_RESET_TYPE_NORMAL)
T3964 4725:165.498 - 0.025ms returns JLINKARM_CM3_RESET_TYPE_NORMAL
T3964 4725:165.527 JLINK_Reset()
T3964 4725:165.557   CPU is running
T3964 4725:165.598   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T3964 4725:166.646   CPU is running
T3964 4725:166.688   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T3964 4725:167.779   Reset: Halt core after reset via DEMCR.VC_CORERESET.
T3964 4725:168.967   Reset: Reset device via AIRCR.SYSRESETREQ.
T3964 4725:169.013   CPU is running
T3964 4725:169.052   CPU_WriteMem(4 bytes @ 0xE000ED0C)
T3964 4725:225.507   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T3964 4725:226.555   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T3964 4725:236.451   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T3964 4725:243.206   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T3964 4725:253.334   CPU_WriteMem(4 bytes @ 0x********)
T3964 4725:254.464   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T3964 4725:255.710   CPU_ReadMem(4 bytes @ 0xE0001000)
T3964 4725:256.797 - 91.269ms
T3964 4725:256.872 JLINK_HasError()
T3964 4725:257.000 JLINK_ReadReg(R15 (PC))
T3964 4725:257.035 - 0.034ms returns 0x00000A80
T3964 4725:257.063 JLINK_ReadReg(XPSR)
T3964 4725:257.090 - 0.026ms returns 0x01000000
T3964 4725:257.121 JLINK_Halt()
T3964 4725:257.148 - 0.026ms returns 0x00
T3964 4725:257.178 JLINK_ReadMemU32(0xE000EDF0, 0x1 Items)
T3964 4725:257.214   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T3964 4725:258.545   Data:  03 00 03 00
T3964 4725:258.591   Debug reg: DHCSR
T3964 4725:258.630 - 1.452ms returns 1 (0x1)
T3964 4725:258.665 JLINK_WriteU32_64(0xE000EDF0, 0xA05F0003)
T3964 4725:258.691   Debug reg: DHCSR
T3964 4725:259.204   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T3964 4725:260.207 - 1.542ms returns 0 (0x00000000)
T3964 4725:260.240 JLINK_WriteU32_64(0xE000EDFC, 0x01000000)
T3964 4725:260.266   Debug reg: DEMCR
T3964 4725:260.308   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T3964 4725:261.324 - 1.082ms returns 0 (0x00000000)
T3964 4725:261.458 JLINK_GetHWStatus(...)
T3964 4725:261.954 - 0.494ms returns 0
T3964 4725:262.097 JLINK_GetNumBPUnits(Type = 0xFFFFFF00)
T3964 4725:262.126 - 0.029ms returns 0x06
T3964 4725:262.154 JLINK_GetNumBPUnits(Type = 0xF0)
T3964 4725:262.183 - 0.028ms returns 0x2000
T3964 4725:262.214 JLINK_GetNumWPUnits()
T3964 4725:262.241 - 0.026ms returns 4
T3964 4725:262.293 JLINK_GetSpeed()
T3964 4725:262.322 - 0.028ms returns 1000
T3964 4725:262.364 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
T3964 4725:262.400   CPU_ReadMem(4 bytes @ 0xE000E004)
T3964 4725:263.365   Data:  01 00 00 00
T3964 4725:263.413 - 1.049ms returns 1 (0x1)
T3964 4725:263.447 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
T3964 4725:263.480   CPU_ReadMem(4 bytes @ 0xE000E004)
T3964 4725:264.440   Data:  01 00 00 00
T3964 4725:264.491 - 1.044ms returns 1 (0x1)
T3964 4725:264.540 JLINK_WriteMemEx(0xE0001000, 0x0000001C Bytes, Flags = 0x02000004)
T3964 4725:264.573   Data:  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
T3964 4725:264.630   CPU_WriteMem(28 bytes @ 0xE0001000)
T3964 4725:266.140 - 1.600ms returns 0x1C
T3964 4725:266.189 JLINK_HasError()
T3964 4725:266.239 JLINK_ReadReg(R15 (PC))
T3964 4725:266.281 - 0.042ms returns 0x00000A80
T3964 4725:266.321 JLINK_ReadReg(XPSR)
T3964 4725:266.359 - 0.038ms returns 0x01000000
T3964 4725:377.896 JLINK_HasError()
T3964 4725:378.024 JLINK_SetResetType(JLINKARM_CM3_RESET_TYPE_NORMAL)
T3964 4725:378.049 - 0.025ms returns JLINKARM_CM3_RESET_TYPE_NORMAL
T3964 4725:378.073 JLINK_Reset()
T3964 4725:378.111   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T3964 4725:379.379   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T3964 4725:380.462   Reset: Halt core after reset via DEMCR.VC_CORERESET.
T3964 4725:381.560   Reset: Reset device via AIRCR.SYSRESETREQ.
T3964 4725:381.607   CPU_WriteMem(4 bytes @ 0xE000ED0C)
T3964 4725:437.168   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T3964 4725:438.346   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T3964 4725:439.321   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T3964 4725:446.296   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T3964 4725:456.769   CPU_WriteMem(4 bytes @ 0x********)
T3964 4725:457.919   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T3964 4725:458.994   CPU_ReadMem(4 bytes @ 0xE0001000)
T3964 4725:460.114 - 82.039ms
T3964 4725:460.249 JLINK_HasError()
T3964 4725:460.300 JLINK_ReadReg(R15 (PC))
T3964 4725:460.342 - 0.041ms returns 0x00000A80
T3964 4725:460.384 JLINK_ReadReg(XPSR)
T3964 4725:460.418 - 0.034ms returns 0x01000000
T3964 4725:460.644 JLINK_ReadMemEx(0x00000A80, 0x3C Bytes, Flags = 0x02000000)
T3964 4725:460.698   CPU_ReadMem(64 bytes @ 0x00000A80)
T3964 4725:462.887    -- Updating C cache (64 bytes @ 0x00000A80)
T3964 4725:462.955    -- Read from C cache (60 bytes @ 0x00000A80)
T3964 4725:463.000   Data:  06 4B 18 47 06 4A 10 60 01 68 81 F3 08 88 40 68 ...
T3964 4725:463.042 - 2.398ms returns 60 (0x3C)
T3964 4726:530.928 JLINK_ReadMemEx(0x0002F6F0, 0x3C Bytes, Flags = 0x02000000)
T3964 4726:530.964   CPU_ReadMem(128 bytes @ 0x0002F6C0)
T3964 4726:534.342    -- Updating C cache (128 bytes @ 0x0002F6C0)
T3964 4726:534.373    -- Read from C cache (60 bytes @ 0x0002F6F0)
T3964 4726:534.389   Data:  29 90 29 A9 3B 48 F9 F7 9F FF 04 46 01 46 3A A0 ...
T3964 4726:534.404 - 3.476ms returns 60 (0x3C)
T3964 4726:660.557 JLINK_HasError()
T3964 4726:660.585 JLINK_ReadReg(R0)
T3964 4726:661.825 - 1.232ms returns 0x00000000
T3964 4726:661.966 JLINK_ReadReg(R1)
T3964 4726:661.980 - 0.013ms returns 0x00000000
T3964 4726:661.990 JLINK_ReadReg(R2)
T3964 4726:662.001 - 0.010ms returns 0x00000000
T3964 4726:662.010 JLINK_ReadReg(R3)
T3964 4726:662.020 - 0.009ms returns 0x00000000
T3964 4726:662.030 JLINK_ReadReg(R4)
T3964 4726:662.040 - 0.009ms returns 0x00000000
T3964 4726:662.050 JLINK_ReadReg(R5)
T3964 4726:662.060 - 0.010ms returns 0x00000000
T3964 4726:662.070 JLINK_ReadReg(R6)
T3964 4726:662.079 - 0.009ms returns 0x00000000
T3964 4726:662.090 JLINK_ReadReg(R7)
T3964 4726:662.099 - 0.009ms returns 0x00000000
T3964 4726:662.109 JLINK_ReadReg(R8)
T3964 4726:662.119 - 0.009ms returns 0x00000000
T3964 4726:662.129 JLINK_ReadReg(R9)
T3964 4726:662.139 - 0.009ms returns 0x00000000
T3964 4726:662.149 JLINK_ReadReg(R10)
T3964 4726:662.159 - 0.009ms returns 0x00000000
T3964 4726:662.169 JLINK_ReadReg(R11)
T3964 4726:662.179 - 0.009ms returns 0x00000000
T3964 4726:662.189 JLINK_ReadReg(R12)
T3964 4726:662.199 - 0.009ms returns 0x00000000
T3964 4726:662.210 JLINK_ReadReg(R13 (SP))
T3964 4726:662.220 - 0.010ms returns 0x20000400
T3964 4726:662.229 JLINK_ReadReg(R14)
T3964 4726:662.239 - 0.009ms returns 0xFFFFFFFF
T3964 4726:662.249 JLINK_ReadReg(R15 (PC))
T3964 4726:662.259 - 0.009ms returns 0x00000A80
T3964 4726:662.269 JLINK_ReadReg(XPSR)
T3964 4726:662.278 - 0.009ms returns 0x01000000
T3964 4726:662.288 JLINK_ReadReg(MSP)
T3964 4726:662.298 - 0.009ms returns 0x20000400
T3964 4726:662.308 JLINK_ReadReg(PSP)
T3964 4726:662.317 - 0.009ms returns 0x00000000
T3964 4726:662.327 JLINK_ReadReg(CFBP)
T3964 4726:662.336 - 0.009ms returns 0x00000000
T3964 4726:662.347 JLINK_ReadReg(FPSCR)
T3964 4726:676.374 - 14.026ms returns 0x00000000
T3964 4726:676.413 JLINK_ReadReg(FPS0)
T3964 4726:676.431 - 0.018ms returns 0x00000000
T3964 4726:676.447 JLINK_ReadReg(FPS1)
T3964 4726:676.462 - 0.015ms returns 0x00000000
T3964 4726:676.477 JLINK_ReadReg(FPS2)
T3964 4726:676.491 - 0.013ms returns 0x00000000
T3964 4726:676.507 JLINK_ReadReg(FPS3)
T3964 4726:676.521 - 0.013ms returns 0x00000000
T3964 4726:676.536 JLINK_ReadReg(FPS4)
T3964 4726:676.551 - 0.014ms returns 0x00000000
T3964 4726:676.565 JLINK_ReadReg(FPS5)
T3964 4726:676.579 - 0.013ms returns 0x00000000
T3964 4726:676.659 JLINK_ReadReg(FPS6)
T3964 4726:676.677 - 0.017ms returns 0x00000000
T3964 4726:676.692 JLINK_ReadReg(FPS7)
T3964 4726:676.711 - 0.019ms returns 0x00000000
T3964 4726:676.726 JLINK_ReadReg(FPS8)
T3964 4726:676.740 - 0.013ms returns 0x00000000
T3964 4726:676.755 JLINK_ReadReg(FPS9)
T3964 4726:676.769 - 0.014ms returns 0x00000000
T3964 4726:676.784 JLINK_ReadReg(FPS10)
T3964 4726:676.798 - 0.013ms returns 0x00000000
T3964 4726:676.813 JLINK_ReadReg(FPS11)
T3964 4726:676.827 - 0.013ms returns 0x00000000
T3964 4726:676.842 JLINK_ReadReg(FPS12)
T3964 4726:676.857 - 0.015ms returns 0x00000000
T3964 4726:676.873 JLINK_ReadReg(FPS13)
T3964 4726:676.886 - 0.013ms returns 0x00000000
T3964 4726:676.901 JLINK_ReadReg(FPS14)
T3964 4726:676.915 - 0.013ms returns 0x00000000
T3964 4726:676.931 JLINK_ReadReg(FPS15)
T3964 4726:676.945 - 0.013ms returns 0x00000000
T3964 4726:676.959 JLINK_ReadReg(FPS16)
T3964 4726:676.973 - 0.013ms returns 0x00000000
T3964 4726:676.988 JLINK_ReadReg(FPS17)
T3964 4726:677.002 - 0.014ms returns 0x00000000
T3964 4726:677.017 JLINK_ReadReg(FPS18)
T3964 4726:677.031 - 0.013ms returns 0x00000000
T3964 4726:677.046 JLINK_ReadReg(FPS19)
T3964 4726:677.060 - 0.013ms returns 0x00000000
T3964 4726:677.075 JLINK_ReadReg(FPS20)
T3964 4726:677.090 - 0.014ms returns 0x00000000
T3964 4726:677.105 JLINK_ReadReg(FPS21)
T3964 4726:677.120 - 0.014ms returns 0x00000000
T3964 4726:677.136 JLINK_ReadReg(FPS22)
T3964 4726:677.155 - 0.018ms returns 0x00000000
T3964 4726:677.170 JLINK_ReadReg(FPS23)
T3964 4726:677.184 - 0.014ms returns 0x00000000
T3964 4726:677.200 JLINK_ReadReg(FPS24)
T3964 4726:677.214 - 0.014ms returns 0x00000000
T3964 4726:677.230 JLINK_ReadReg(FPS25)
T3964 4726:677.244 - 0.014ms returns 0x00000000
T3964 4726:677.260 JLINK_ReadReg(FPS26)
T3964 4726:677.275 - 0.014ms returns 0x00000000
T3964 4726:677.290 JLINK_ReadReg(FPS27)
T3964 4726:677.305 - 0.014ms returns 0x00000000
T3964 4726:677.321 JLINK_ReadReg(FPS28)
T3964 4726:677.335 - 0.014ms returns 0x00000000
T3964 4726:677.350 JLINK_ReadReg(FPS29)
T3964 4726:677.366 - 0.015ms returns 0x00000000
T3964 4726:677.381 JLINK_ReadReg(FPS30)
T3964 4726:677.395 - 0.014ms returns 0x00000000
T3964 4726:677.411 JLINK_ReadReg(FPS31)
T3964 4726:677.425 - 0.014ms returns 0x00000000
T393C 4726:767.635 JLINK_ReadMemEx(0x00000A80, 0x2 Bytes, Flags = 0x02000000)
T393C 4726:767.691    -- Read from C cache (2 bytes @ 0x00000A80)
T393C 4726:767.707   Data:  06 4B
T393C 4726:767.722 - 0.087ms returns 2 (0x2)
T393C 4726:767.734 JLINK_HasError()
T393C 4726:767.753 JLINK_SetBPEx(Addr = 0x0002B768, Type = 0xFFFFFFF2)
T393C 4726:767.766 - 0.013ms returns 0x00000001
T393C 4726:767.777 JLINK_HasError()
T393C 4726:767.787 JLINK_SetBPEx(Addr = 0x0002B790, Type = 0xFFFFFFF2)
T393C 4726:767.798 - 0.010ms returns 0x00000002
T393C 4726:767.808 JLINK_HasError()
T393C 4726:767.819 JLINK_SetBPEx(Addr = 0x0002B774, Type = 0xFFFFFFF2)
T393C 4726:767.829 - 0.010ms returns 0x00000003
T393C 4726:767.839 JLINK_HasError()
T393C 4726:767.850 JLINK_SetBPEx(Addr = 0x0002B76A, Type = 0xFFFFFFF2)
T393C 4726:767.860 - 0.010ms returns 0x00000004
T393C 4726:767.871 JLINK_HasError()
T393C 4726:767.881 JLINK_SetBPEx(Addr = 0x00029C20, Type = 0xFFFFFFF2)
T393C 4726:767.891 - 0.010ms returns 0x00000005
T393C 4726:767.902 JLINK_HasError()
T393C 4726:767.913 JLINK_SetBPEx(Addr = 0x00029C00, Type = 0xFFFFFFF2)
T393C 4726:767.922 - 0.009ms returns 0x00000006
T393C 4726:767.933 JLINK_HasError()
T393C 4726:767.943 JLINK_SetBPEx(Addr = 0x0002F7C4, Type = 0xFFFFFFF2)
T393C 4726:767.957    -- BP[0] @ 0x0002B768 converted into FlashBP
T393C 4726:767.971 - 0.028ms returns 0x00000007
T393C 4726:767.982 JLINK_HasError()
T393C 4726:767.992 JLINK_SetBPEx(Addr = 0x0002F6F0, Type = 0xFFFFFFF2)
T393C 4726:768.003    -- BP[1] @ 0x0002B790 converted into FlashBP
T393C 4726:768.018 - 0.025ms returns 0x00000008
T393C 4726:768.028 JLINK_HasError()
T393C 4726:768.039 JLINK_SetBPEx(Addr = 0x00029C6E, Type = 0xFFFFFFF2)
T393C 4726:768.049    -- BP[2] @ 0x0002B774 converted into FlashBP
T393C 4726:768.071 - 0.032ms returns 0x00000009
T393C 4726:768.082 JLINK_HasError()
T393C 4726:768.092 JLINK_SetBPEx(Addr = 0x00029C68, Type = 0xFFFFFFF2)
T393C 4726:768.103    -- BP[3] @ 0x0002B76A converted into FlashBP
T393C 4726:768.117 - 0.025ms returns 0x0000000A
T393C 4726:768.128 JLINK_HasError()
T393C 4726:768.138 JLINK_HasError()
T393C 4726:768.150 JLINK_Go()
T393C 4726:768.163    -- Read from C cache (2 bytes @ 0x00000A80)
T393C 4726:768.182   CPU_ReadMem(4 bytes @ 0xE000ED18)
T393C 4726:769.300   CPU_WriteMem(4 bytes @ 0xE000ED18)
T393C 4726:770.205   CPU_ReadMem(4 bytes @ 0xE000ED18)
T393C 4726:771.087   CPU_WriteMem(4 bytes @ 0xE000ED18)
T393C 4726:772.065    -- Read from C cache (4 bytes @ 0x00000A9C)
T393C 4726:772.099   -- Simulated
T393C 4726:772.116    -- Read from C cache (2 bytes @ 0x00000A82)
T393C 4726:772.132   -- Simulated
T393C 4726:772.150   CPU_ReadMem(64 bytes @ 0x00000400)
T393C 4726:774.128    -- Updating C cache (64 bytes @ 0x00000400)
T393C 4726:774.151    -- Read from C cache (2 bytes @ 0x00000416)
T393C 4726:774.167    -- Read from C cache (2 bytes @ 0x00000418)
T393C 4726:774.183   -- Simulated
T393C 4726:774.198    -- Read from C cache (2 bytes @ 0x0000041A)
T393C 4726:774.216   CPU_ReadMem(64 bytes @ 0x00000FC0)
T393C 4726:776.293    -- Updating C cache (64 bytes @ 0x00000FC0)
T393C 4726:776.376    -- Read from C cache (4 bytes @ 0x00000FFC)
T393C 4726:776.420   -- Simulated
T393C 4726:776.463    -- Read from C cache (2 bytes @ 0x0000041C)
T393C 4726:776.502    -- Read from C cache (2 bytes @ 0x0000041E)
T393C 4726:776.539   -- Simulated
T393C 4726:776.577    -- Read from C cache (2 bytes @ 0x00000420)
T393C 4726:776.615   -- Simulated
T393C 4726:776.653    -- Read from C cache (2 bytes @ 0x00000422)
T393C 4726:776.690   -- Simulated
T393C 4726:776.734   CPU_ReadMem(64 bytes @ 0x00000440)
T393C 4726:778.868    -- Updating C cache (64 bytes @ 0x00000440)
T393C 4726:778.994    -- Read from C cache (2 bytes @ 0x0000046C)
T393C 4726:779.047   CPU_ReadMem(64 bytes @ 0x10001000)
T393C 4726:781.179    -- Updating C cache (64 bytes @ 0x10001000)
T393C 4726:781.262    -- Read from C cache (4 bytes @ 0x10001018)
T393C 4726:781.860   -- Simulated
T393C 4726:781.917    -- Read from C cache (2 bytes @ 0x0000046E)
T393C 4726:781.954   -- Simulated
T393C 4726:781.989    -- Read from C cache (2 bytes @ 0x00000426)
T393C 4726:782.024   -- Simulated
T393C 4726:782.069   CPU_ReadMem(4 bytes @ 0xE000ED90)
T393C 4726:783.137   CPU_ReadMem(4 bytes @ 0xE000ED94)
T393C 4726:784.196    -- Start of preparing flash programming
T393C 4726:784.269    -- Calculating RAM usage
T393C 4726:784.468    -- RAM usage = 9908 Bytes
T393C 4726:784.507    -- Preserving CPU registers
T393C 4726:784.566    -- Preparing target
T393C 4726:784.603    -- Preserving target RAM temporarily used for programming
T393C 4726:967.000    -- Downloading RAMCode
T393C 4727:013.677    -- Preparing RAMCode
T393C 4727:030.730    -- End of preparing flash programming
T393C 4727:030.886   CPU_ReadMem(4096 bytes @ 0x0002B000)
T393C 4727:106.735    -- Updating flash cache (4096 bytes @ 0x0002B000)
T393C 4727:106.854    -- Updating flash cache invalid range 0x0002B000 - 0x0002BFFF (4096 bytes)
T393C 4727:106.903    -- Programming range 0x0002B000 - 0x0002BFFF (  1 Sector, 4 KB)
T393C 4727:419.946    -- Start of restoring
T393C 4727:420.038    -- Restoring RAMCode
T393C 4727:432.502    -- Restoring target memory
T393C 4727:604.337    -- Restore target
T393C 4727:604.519    -- Restoring CPU registers
T393C 4727:604.616    -- End of restoring
T393C 4727:638.705   CPU_WriteMem(4 bytes @ 0x********)
T393C 4727:640.134   CPU_WriteMem(4 bytes @ 0x********)
T393C 4727:641.235   CPU_WriteMem(4 bytes @ 0x********)
T393C 4727:642.369   CPU_WriteMem(4 bytes @ 0x********)
T393C 4727:643.453   CPU_WriteMem(4 bytes @ 0x********)
T393C 4727:644.706   CPU_WriteMem(4 bytes @ 0x********)
T393C 4727:646.042   CPU_ReadMem(4 bytes @ 0xE0001000)
T393C 4727:647.584   CPU_WriteMem(4 bytes @ 0xE0002008)
T393C 4727:647.723   CPU_WriteMem(4 bytes @ 0xE000200C)
T393C 4727:647.780   CPU_WriteMem(4 bytes @ 0xE0002010)
T393C 4727:647.832   CPU_WriteMem(4 bytes @ 0xE0002014)
T393C 4727:647.892   CPU_WriteMem(4 bytes @ 0xE0002018)
T393C 4727:647.945   CPU_WriteMem(4 bytes @ 0xE000201C)
T393C 4727:690.904   Memory map 'after startup completion point' is active
T393C 4727:691.025 - 922.873ms
T393C 4727:791.698 JLINK_HasError()
T393C 4727:791.771 JLINK_IsHalted()
T393C 4727:801.026 - 9.253ms returns TRUE
T393C 4727:801.137 JLINK_HasError()
T393C 4727:801.169 JLINK_Halt()
T393C 4727:801.195 - 0.025ms returns 0x00
T393C 4727:801.225 JLINK_IsHalted()
T393C 4727:801.249 - 0.023ms returns TRUE
T393C 4727:801.278 JLINK_IsHalted()
T393C 4727:801.301 - 0.022ms returns TRUE
T393C 4727:801.329 JLINK_IsHalted()
T393C 4727:801.352 - 0.023ms returns TRUE
T393C 4727:801.381 JLINK_HasError()
T393C 4727:801.412 JLINK_ReadReg(R15 (PC))
T393C 4727:801.440 - 0.028ms returns 0x0002B768
T393C 4727:801.468 JLINK_ReadReg(XPSR)
T393C 4727:801.493 - 0.024ms returns 0x61000000
T393C 4727:801.601 JLINK_HasError()
T393C 4727:801.637 JLINK_ClrBPEx(BPHandle = 0x00000001)
T393C 4727:801.661 - 0.024ms returns 0x00
T393C 4727:801.690 JLINK_HasError()
T393C 4727:801.717 JLINK_ClrBPEx(BPHandle = 0x00000002)
T393C 4727:801.739 - 0.022ms returns 0x00
T393C 4727:801.764 JLINK_HasError()
T393C 4727:801.788 JLINK_ClrBPEx(BPHandle = 0x00000003)
T393C 4727:801.811 - 0.023ms returns 0x00
T393C 4727:801.835 JLINK_HasError()
T393C 4727:801.858 JLINK_ClrBPEx(BPHandle = 0x00000004)
T393C 4727:801.880 - 0.022ms returns 0x00
T393C 4727:801.904 JLINK_HasError()
T393C 4727:801.928 JLINK_ClrBPEx(BPHandle = 0x00000005)
T393C 4727:801.950 - 0.022ms returns 0x00
T393C 4727:801.975 JLINK_HasError()
T393C 4727:802.000 JLINK_ClrBPEx(BPHandle = 0x00000006)
T393C 4727:802.024 - 0.024ms returns 0x00
T393C 4727:802.049 JLINK_HasError()
T393C 4727:802.074 JLINK_ClrBPEx(BPHandle = 0x00000007)
T393C 4727:802.099 - 0.024ms returns 0x00
T393C 4727:802.125 JLINK_HasError()
T393C 4727:802.150 JLINK_ClrBPEx(BPHandle = 0x00000008)
T393C 4727:802.174 - 0.023ms returns 0x00
T393C 4727:802.202 JLINK_HasError()
T393C 4727:802.228 JLINK_ClrBPEx(BPHandle = 0x00000009)
T393C 4727:802.252 - 0.024ms returns 0x00
T393C 4727:802.284 JLINK_HasError()
T393C 4727:802.321 JLINK_ClrBPEx(BPHandle = 0x0000000A)
T393C 4727:802.348 - 0.027ms returns 0x00
T393C 4727:802.377 JLINK_HasError()
T393C 4727:802.407 JLINK_HasError()
T393C 4727:802.439 JLINK_ReadMemU32(0xE000ED30, 0x1 Items)
T393C 4727:802.481   CPU_ReadMem(4 bytes @ 0xE000ED30)
T393C 4727:803.521   Data:  02 00 00 00
T393C 4727:803.587 - 1.148ms returns 1 (0x1)
T393C 4727:803.617 JLINK_ReadMemU32(0xE0001028, 0x1 Items)
T393C 4727:803.650   CPU_ReadMem(4 bytes @ 0xE0001028)
T393C 4727:804.680   Data:  00 00 00 00
T393C 4727:804.747   Debug reg: DWT_FUNC[0]
T393C 4727:804.807 - 1.189ms returns 1 (0x1)
T393C 4727:804.851 JLINK_ReadMemU32(0xE0001038, 0x1 Items)
T393C 4727:804.896   CPU_ReadMem(4 bytes @ 0xE0001038)
T393C 4727:806.083   Data:  00 02 00 00
T393C 4727:806.207   Debug reg: DWT_FUNC[1]
T393C 4727:806.260 - 1.408ms returns 1 (0x1)
T393C 4727:806.306 JLINK_ReadMemU32(0xE0001048, 0x1 Items)
T393C 4727:806.359   CPU_ReadMem(4 bytes @ 0xE0001048)
T393C 4727:807.428   Data:  00 00 00 00
T393C 4727:807.485   Debug reg: DWT_FUNC[2]
T393C 4727:807.529 - 1.223ms returns 1 (0x1)
T393C 4727:807.565 JLINK_ReadMemU32(0xE0001058, 0x1 Items)
T393C 4727:807.600   CPU_ReadMem(4 bytes @ 0xE0001058)
T393C 4727:808.710   Data:  00 00 00 00
T393C 4727:808.753   Debug reg: DWT_FUNC[3]
T393C 4727:808.794 - 1.228ms returns 1 (0x1)
T393C 4727:808.888 JLINK_HasError()
T393C 4727:808.921 JLINK_ReadReg(R0)
T393C 4727:808.953 - 0.031ms returns 0x0002B769
T393C 4727:808.983 JLINK_ReadReg(R1)
T393C 4727:809.011 - 0.028ms returns 0x20005C18
T393C 4727:809.041 JLINK_ReadReg(R2)
T393C 4727:809.069 - 0.028ms returns 0x00000000
T393C 4727:809.098 JLINK_ReadReg(R3)
T393C 4727:809.145 - 0.046ms returns 0x000279D1
T393C 4727:809.175 JLINK_ReadReg(R4)
T393C 4727:809.204 - 0.028ms returns 0x000321DC
T393C 4727:809.234 JLINK_ReadReg(R5)
T393C 4727:809.262 - 0.028ms returns 0x000321DC
T393C 4727:809.293 JLINK_ReadReg(R6)
T393C 4727:809.321 - 0.027ms returns 0x10001000
T393C 4727:809.350 JLINK_ReadReg(R7)
T393C 4727:809.377 - 0.027ms returns 0x00000000
T393C 4727:809.407 JLINK_ReadReg(R8)
T393C 4727:809.435 - 0.027ms returns 0x00000000
T393C 4727:809.466 JLINK_ReadReg(R9)
T393C 4727:809.494 - 0.028ms returns 0x00000000
T393C 4727:809.524 JLINK_ReadReg(R10)
T393C 4727:809.551 - 0.027ms returns 0x00000000
T393C 4727:809.582 JLINK_ReadReg(R11)
T393C 4727:809.611 - 0.028ms returns 0x00000000
T393C 4727:809.640 JLINK_ReadReg(R12)
T393C 4727:809.668 - 0.027ms returns 0x00000000
T393C 4727:809.697 JLINK_ReadReg(R13 (SP))
T393C 4727:809.725 - 0.028ms returns 0x20005C18
T393C 4727:809.755 JLINK_ReadReg(R14)
T393C 4727:809.783 - 0.028ms returns 0x000274C1
T393C 4727:809.813 JLINK_ReadReg(R15 (PC))
T393C 4727:809.842 - 0.029ms returns 0x0002B768
T393C 4727:809.872 JLINK_ReadReg(XPSR)
T393C 4727:809.900 - 0.027ms returns 0x61000000
T393C 4727:809.931 JLINK_ReadReg(MSP)
T393C 4727:809.959 - 0.028ms returns 0x20005C18
T393C 4727:809.990 JLINK_ReadReg(PSP)
T393C 4727:810.019 - 0.028ms returns 0x00000000
T393C 4727:810.049 JLINK_ReadReg(CFBP)
T393C 4727:810.075 - 0.025ms returns 0x00000000
T393C 4727:810.104 JLINK_ReadReg(FPSCR)
T393C 4727:824.468 - 14.362ms returns 0x00000000
T393C 4727:824.544 JLINK_ReadReg(FPS0)
T393C 4727:824.579 - 0.035ms returns 0x00000000
T393C 4727:824.608 JLINK_ReadReg(FPS1)
T393C 4727:824.634 - 0.026ms returns 0x00000000
T393C 4727:824.663 JLINK_ReadReg(FPS2)
T393C 4727:824.690 - 0.026ms returns 0x00000000
T393C 4727:824.718 JLINK_ReadReg(FPS3)
T393C 4727:824.746 - 0.027ms returns 0x00000000
T393C 4727:824.773 JLINK_ReadReg(FPS4)
T393C 4727:824.800 - 0.026ms returns 0x00000000
T393C 4727:824.827 JLINK_ReadReg(FPS5)
T393C 4727:824.853 - 0.026ms returns 0x00000000
T393C 4727:824.881 JLINK_ReadReg(FPS6)
T393C 4727:824.908 - 0.027ms returns 0x00000000
T393C 4727:824.937 JLINK_ReadReg(FPS7)
T393C 4727:824.964 - 0.027ms returns 0x00000000
T393C 4727:824.991 JLINK_ReadReg(FPS8)
T393C 4727:825.019 - 0.027ms returns 0x00000000
T393C 4727:825.048 JLINK_ReadReg(FPS9)
T393C 4727:825.073 - 0.025ms returns 0x00000000
T393C 4727:825.101 JLINK_ReadReg(FPS10)
T393C 4727:825.127 - 0.026ms returns 0x00000000
T393C 4727:825.156 JLINK_ReadReg(FPS11)
T393C 4727:825.182 - 0.025ms returns 0x00000000
T393C 4727:825.210 JLINK_ReadReg(FPS12)
T393C 4727:825.234 - 0.024ms returns 0x00000000
T393C 4727:825.259 JLINK_ReadReg(FPS13)
T393C 4727:825.284 - 0.024ms returns 0x00000000
T393C 4727:825.310 JLINK_ReadReg(FPS14)
T393C 4727:825.334 - 0.024ms returns 0x00000000
T393C 4727:825.361 JLINK_ReadReg(FPS15)
T393C 4727:825.386 - 0.024ms returns 0x00000000
T393C 4727:825.414 JLINK_ReadReg(FPS16)
T393C 4727:825.439 - 0.024ms returns 0x00000000
T393C 4727:825.466 JLINK_ReadReg(FPS17)
T393C 4727:825.492 - 0.024ms returns 0x00000000
T393C 4727:825.527 JLINK_ReadReg(FPS18)
T393C 4727:825.553 - 0.025ms returns 0x00000000
T393C 4727:825.580 JLINK_ReadReg(FPS19)
T393C 4727:825.605 - 0.024ms returns 0x00000000
T393C 4727:825.632 JLINK_ReadReg(FPS20)
T393C 4727:825.657 - 0.025ms returns 0x00000000
T393C 4727:825.685 JLINK_ReadReg(FPS21)
T393C 4727:825.710 - 0.024ms returns 0x00000000
T393C 4727:825.736 JLINK_ReadReg(FPS22)
T393C 4727:825.761 - 0.024ms returns 0x00000000
T393C 4727:825.788 JLINK_ReadReg(FPS23)
T393C 4727:825.812 - 0.023ms returns 0x00000000
T393C 4727:825.839 JLINK_ReadReg(FPS24)
T393C 4727:825.865 - 0.025ms returns 0x00000000
T393C 4727:825.893 JLINK_ReadReg(FPS25)
T393C 4727:825.920 - 0.026ms returns 0x00000000
T393C 4727:825.948 JLINK_ReadReg(FPS26)
T393C 4727:825.974 - 0.025ms returns 0x00000000
T393C 4727:826.002 JLINK_ReadReg(FPS27)
T393C 4727:826.028 - 0.026ms returns 0x00000000
T393C 4727:826.055 JLINK_ReadReg(FPS28)
T393C 4727:826.217 - 0.161ms returns 0x00000000
T393C 4727:826.246 JLINK_ReadReg(FPS29)
T393C 4727:826.273 - 0.026ms returns 0x00000000
T393C 4727:826.300 JLINK_ReadReg(FPS30)
T393C 4727:826.327 - 0.027ms returns 0x00000000
T393C 4727:826.355 JLINK_ReadReg(FPS31)
T393C 4727:826.381 - 0.025ms returns 0x00000000
T3964 4727:826.946 JLINK_ReadMemEx(0x00000000, 0x1 Bytes, Flags = 0x02000000)
T3964 4727:827.071   CPU_ReadMem(64 bytes @ 0x00000000)
T3964 4727:829.827    -- Updating C cache (64 bytes @ 0x00000000)
T3964 4727:830.037    -- Read from C cache (1 bytes @ 0x00000000)
T3964 4727:830.094   Data:  00
T3964 4727:830.205 - 3.260ms returns 1 (0x1)
T3964 4727:830.279 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T3964 4727:830.314    -- Read from C cache (4 bytes @ 0x00000000)
T3964 4727:830.357   Data:  00 04 00 20
T3964 4727:830.399 - 0.120ms returns 4 (0x4)
T3964 4727:840.381 JLINK_HasError()
T3964 4727:840.497 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T3964 4727:840.558   CPU_ReadMem(4 bytes @ 0xE0001004)
T3964 4727:841.770   Data:  C9 8A 87 01
T3964 4727:841.827   Debug reg: DWT_CYCCNT
T3964 4727:841.874 - 1.377ms returns 1 (0x1)
T393C 4729:131.860 JLINK_ReadMemEx(0x0002B768, 0x2 Bytes, Flags = 0x02000000)
T393C 4729:131.938    -- Merging zombie BP[0]: 0xB08A @ 0x0002B768
T393C 4729:131.971    -- Merging zombie BP[0]: 0xB08A @ 0x0002B768
T393C 4729:132.002   Data:  8A B0
T393C 4729:132.033 - 0.173ms returns 2 (0x2)
T393C 4729:132.057 JLINK_HasError()
T393C 4729:132.090 JLINK_Step()
T393C 4729:133.142    -- Merging zombie BP[0]: 0xB08A @ 0x0002B768
T393C 4729:133.181    -- Merging zombie BP[0]: 0xB08A @ 0x0002B768
T393C 4729:133.216   -- Simulated
T393C 4729:133.247 - 1.155ms returns 0
T393C 4729:133.280 JLINK_HasError()
T393C 4729:133.308 JLINK_ReadReg(R15 (PC))
T393C 4729:133.332 - 0.023ms returns 0x0002B76A
T393C 4729:133.358 JLINK_ReadReg(XPSR)
T393C 4729:133.380 - 0.022ms returns 0x61000000
T393C 4729:133.471 JLINK_HasError()
T393C 4729:133.504 JLINK_ReadReg(R0)
T393C 4729:133.527 - 0.023ms returns 0x0002B769
T393C 4729:133.548 JLINK_ReadReg(R1)
T393C 4729:133.569 - 0.020ms returns 0x20005C18
T393C 4729:133.590 JLINK_ReadReg(R2)
T393C 4729:133.611 - 0.020ms returns 0x00000000
T393C 4729:133.632 JLINK_ReadReg(R3)
T393C 4729:133.653 - 0.020ms returns 0x000279D1
T393C 4729:133.674 JLINK_ReadReg(R4)
T393C 4729:133.695 - 0.020ms returns 0x000321DC
T393C 4729:133.716 JLINK_ReadReg(R5)
T393C 4729:133.737 - 0.020ms returns 0x000321DC
T393C 4729:133.758 JLINK_ReadReg(R6)
T393C 4729:133.785 - 0.027ms returns 0x10001000
T393C 4729:133.808 JLINK_ReadReg(R7)
T393C 4729:133.828 - 0.020ms returns 0x00000000
T393C 4729:133.850 JLINK_ReadReg(R8)
T393C 4729:133.871 - 0.020ms returns 0x00000000
T393C 4729:133.892 JLINK_ReadReg(R9)
T393C 4729:133.913 - 0.020ms returns 0x00000000
T393C 4729:133.935 JLINK_ReadReg(R10)
T393C 4729:133.955 - 0.020ms returns 0x00000000
T393C 4729:133.977 JLINK_ReadReg(R11)
T393C 4729:133.997 - 0.020ms returns 0x00000000
T393C 4729:134.019 JLINK_ReadReg(R12)
T393C 4729:134.039 - 0.020ms returns 0x00000000
T393C 4729:134.061 JLINK_ReadReg(R13 (SP))
T393C 4729:134.082 - 0.020ms returns 0x20005BF0
T393C 4729:134.103 JLINK_ReadReg(R14)
T393C 4729:134.123 - 0.020ms returns 0x000274C1
T393C 4729:134.145 JLINK_ReadReg(R15 (PC))
T393C 4729:134.165 - 0.020ms returns 0x0002B76A
T393C 4729:134.187 JLINK_ReadReg(XPSR)
T393C 4729:134.207 - 0.020ms returns 0x61000000
T393C 4729:134.230 JLINK_ReadReg(MSP)
T393C 4729:134.251 - 0.020ms returns 0x20005BF0
T393C 4729:134.272 JLINK_ReadReg(PSP)
T393C 4729:134.292 - 0.020ms returns 0x00000000
T393C 4729:134.314 JLINK_ReadReg(CFBP)
T393C 4729:134.334 - 0.020ms returns 0x00000000
T393C 4729:134.356 JLINK_ReadReg(FPSCR)
T393C 4729:134.377 - 0.020ms returns 0x00000000
T393C 4729:134.398 JLINK_ReadReg(FPS0)
T393C 4729:134.419 - 0.020ms returns 0x00000000
T393C 4729:134.441 JLINK_ReadReg(FPS1)
T393C 4729:134.461 - 0.020ms returns 0x00000000
T393C 4729:134.483 JLINK_ReadReg(FPS2)
T393C 4729:134.595 - 0.111ms returns 0x00000000
T393C 4729:134.617 JLINK_ReadReg(FPS3)
T393C 4729:134.638 - 0.020ms returns 0x00000000
T393C 4729:134.660 JLINK_ReadReg(FPS4)
T393C 4729:134.680 - 0.020ms returns 0x00000000
T393C 4729:134.702 JLINK_ReadReg(FPS5)
T393C 4729:134.722 - 0.020ms returns 0x00000000
T393C 4729:134.743 JLINK_ReadReg(FPS6)
T393C 4729:134.764 - 0.020ms returns 0x00000000
T393C 4729:134.785 JLINK_ReadReg(FPS7)
T393C 4729:134.806 - 0.020ms returns 0x00000000
T393C 4729:134.828 JLINK_ReadReg(FPS8)
T393C 4729:134.848 - 0.020ms returns 0x00000000
T393C 4729:134.883 JLINK_ReadReg(FPS9)
T393C 4729:134.905 - 0.021ms returns 0x00000000
T393C 4729:134.926 JLINK_ReadReg(FPS10)
T393C 4729:134.947 - 0.020ms returns 0x00000000
T393C 4729:134.968 JLINK_ReadReg(FPS11)
T393C 4729:134.989 - 0.020ms returns 0x00000000
T393C 4729:135.011 JLINK_ReadReg(FPS12)
T393C 4729:135.031 - 0.020ms returns 0x00000000
T393C 4729:135.053 JLINK_ReadReg(FPS13)
T393C 4729:135.073 - 0.020ms returns 0x00000000
T393C 4729:135.094 JLINK_ReadReg(FPS14)
T393C 4729:135.115 - 0.020ms returns 0x00000000
T393C 4729:135.137 JLINK_ReadReg(FPS15)
T393C 4729:135.157 - 0.020ms returns 0x00000000
T393C 4729:135.179 JLINK_ReadReg(FPS16)
T393C 4729:135.199 - 0.020ms returns 0x00000000
T393C 4729:135.221 JLINK_ReadReg(FPS17)
T393C 4729:135.242 - 0.020ms returns 0x00000000
T393C 4729:135.265 JLINK_ReadReg(FPS18)
T393C 4729:135.293 - 0.027ms returns 0x00000000
T393C 4729:135.316 JLINK_ReadReg(FPS19)
T393C 4729:135.357 - 0.040ms returns 0x00000000
T393C 4729:135.379 JLINK_ReadReg(FPS20)
T393C 4729:135.400 - 0.020ms returns 0x00000000
T393C 4729:135.421 JLINK_ReadReg(FPS21)
T393C 4729:135.442 - 0.020ms returns 0x00000000
T393C 4729:135.464 JLINK_ReadReg(FPS22)
T393C 4729:135.484 - 0.020ms returns 0x00000000
T393C 4729:135.506 JLINK_ReadReg(FPS23)
T393C 4729:135.526 - 0.020ms returns 0x00000000
T393C 4729:135.548 JLINK_ReadReg(FPS24)
T393C 4729:135.568 - 0.020ms returns 0x00000000
T393C 4729:135.590 JLINK_ReadReg(FPS25)
T393C 4729:135.611 - 0.020ms returns 0x00000000
T393C 4729:135.633 JLINK_ReadReg(FPS26)
T393C 4729:135.654 - 0.020ms returns 0x00000000
T393C 4729:135.675 JLINK_ReadReg(FPS27)
T393C 4729:135.696 - 0.020ms returns 0x00000000
T393C 4729:135.718 JLINK_ReadReg(FPS28)
T393C 4729:135.738 - 0.020ms returns 0x00000000
T393C 4729:135.760 JLINK_ReadReg(FPS29)
T393C 4729:135.780 - 0.020ms returns 0x00000000
T393C 4729:135.802 JLINK_ReadReg(FPS30)
T393C 4729:135.822 - 0.020ms returns 0x00000000
T393C 4729:135.844 JLINK_ReadReg(FPS31)
T393C 4729:135.864 - 0.020ms returns 0x00000000
T3964 4729:143.591 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T3964 4729:143.663    -- Read from C cache (4 bytes @ 0x00000000)
T3964 4729:143.697   Data:  00 04 00 20
T3964 4729:143.730 - 0.140ms returns 4 (0x4)
T3964 4729:152.149 JLINK_HasError()
T3964 4729:152.205 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T3964 4729:152.229   Data:  CA 8A 87 01
T3964 4729:152.250   Debug reg: DWT_CYCCNT
T3964 4729:152.270 - 0.065ms returns 1 (0x1)
T393C 4729:842.652 JLINK_ReadMemEx(0x0002B76A, 0x2 Bytes, Flags = 0x02000000)
T393C 4729:842.727    -- Merging zombie BP[3]: 0x2218 @ 0x0002B76A
T393C 4729:842.758    -- Merging zombie BP[3]: 0x2218 @ 0x0002B76A
T393C 4729:842.789   Data:  18 22
T393C 4729:842.821 - 0.169ms returns 2 (0x2)
T393C 4729:842.844 JLINK_HasError()
T393C 4729:842.870 JLINK_Step()
T393C 4729:843.831    -- Merging zombie BP[3]: 0x2218 @ 0x0002B76A
T393C 4729:843.856    -- Merging zombie BP[3]: 0x2218 @ 0x0002B76A
T393C 4729:843.875   -- Simulated
T393C 4729:843.889 - 1.019ms returns 0
T393C 4729:843.903 JLINK_HasError()
T393C 4729:843.916 JLINK_ReadReg(R15 (PC))
T393C 4729:843.929 - 0.012ms returns 0x0002B76C
T393C 4729:843.940 JLINK_ReadReg(XPSR)
T393C 4729:843.954 - 0.014ms returns 0x21000000
T393C 4729:843.971 JLINK_HasError()
T393C 4729:843.982 JLINK_SetBPEx(Addr = 0x0002B768, Type = 0xFFFFFFF2)
T393C 4729:843.993 - 0.011ms returns 0x0000000B
T393C 4729:844.004 JLINK_HasError()
T393C 4729:844.024 JLINK_SetBPEx(Addr = 0x0002B790, Type = 0xFFFFFFF2)
T393C 4729:844.034 - 0.010ms returns 0x0000000C
T393C 4729:844.045 JLINK_HasError()
T393C 4729:844.056 JLINK_SetBPEx(Addr = 0x0002B774, Type = 0xFFFFFFF2)
T393C 4729:844.066 - 0.010ms returns 0x0000000D
T393C 4729:844.076 JLINK_HasError()
T393C 4729:844.087 JLINK_SetBPEx(Addr = 0x0002B76A, Type = 0xFFFFFFF2)
T393C 4729:844.097 - 0.009ms returns 0x0000000E
T393C 4729:844.109 JLINK_HasError()
T393C 4729:844.120 JLINK_SetBPEx(Addr = 0x00029C20, Type = 0xFFFFFFF2)
T393C 4729:844.132 - 0.012ms returns 0x0000000F
T393C 4729:844.143 JLINK_HasError()
T393C 4729:844.154 JLINK_SetBPEx(Addr = 0x00029C00, Type = 0xFFFFFFF2)
T393C 4729:844.166 - 0.012ms returns 0x00000010
T393C 4729:844.177 JLINK_HasError()
T393C 4729:844.188 JLINK_SetBPEx(Addr = 0x0002F7C4, Type = 0xFFFFFFF2)
T393C 4729:844.198 - 0.010ms returns 0x00000011
T393C 4729:844.208 JLINK_HasError()
T393C 4729:844.219 JLINK_SetBPEx(Addr = 0x0002F6F0, Type = 0xFFFFFFF2)
T393C 4729:844.229 - 0.010ms returns 0x00000012
T393C 4729:844.240 JLINK_HasError()
T393C 4729:844.251 JLINK_SetBPEx(Addr = 0x00029C6E, Type = 0xFFFFFFF2)
T393C 4729:844.261 - 0.010ms returns 0x00000013
T393C 4729:844.271 JLINK_HasError()
T393C 4729:844.281 JLINK_SetBPEx(Addr = 0x00029C68, Type = 0xFFFFFFF2)
T393C 4729:844.291 - 0.009ms returns 0x00000014
T393C 4729:844.301 JLINK_HasError()
T393C 4729:844.311 JLINK_HasError()
T393C 4729:844.322 JLINK_Go()
T393C 4729:844.340   CPU_WriteMem(4 bytes @ 0x********)
T393C 4729:845.335   CPU_WriteMem(4 bytes @ 0x********)
T393C 4729:846.314   CPU_WriteMem(4 bytes @ 0x********)
T393C 4729:847.332   CPU_WriteMem(4 bytes @ 0x********)
T393C 4729:848.348   CPU_WriteMem(4 bytes @ 0x********)
T393C 4729:849.402   CPU_WriteMem(4 bytes @ 0x********)
T393C 4729:850.382   CPU_ReadMem(4 bytes @ 0xE0001000)
T393C 4729:851.398   CPU_WriteMem(4 bytes @ 0xE0001004)
T393C 4729:855.895 - 11.572ms
T393C 4729:956.073 JLINK_HasError()
T393C 4729:956.165 JLINK_IsHalted()
T393C 4729:965.193 - 9.026ms returns TRUE
T393C 4729:965.298 JLINK_HasError()
T393C 4729:965.342 JLINK_Halt()
T393C 4729:965.381 - 0.039ms returns 0x00
T393C 4729:965.420 JLINK_IsHalted()
T393C 4729:965.455 - 0.034ms returns TRUE
T393C 4729:965.495 JLINK_IsHalted()
T393C 4729:965.529 - 0.033ms returns TRUE
T393C 4729:965.571 JLINK_IsHalted()
T393C 4729:965.605 - 0.033ms returns TRUE
T393C 4729:965.647 JLINK_HasError()
T393C 4729:965.691 JLINK_ReadReg(R15 (PC))
T393C 4729:965.729 - 0.038ms returns 0x0002B774
T393C 4729:965.770 JLINK_ReadReg(XPSR)
T393C 4729:965.810 - 0.040ms returns 0x81000000
T393C 4729:965.857 JLINK_HasError()
T393C 4729:965.899 JLINK_ClrBPEx(BPHandle = 0x0000000B)
T393C 4729:965.934 - 0.035ms returns 0x00
T393C 4729:965.974 JLINK_HasError()
T393C 4729:966.016 JLINK_ClrBPEx(BPHandle = 0x0000000C)
T393C 4729:966.050 - 0.035ms returns 0x00
T393C 4729:966.092 JLINK_HasError()
T393C 4729:966.133 JLINK_ClrBPEx(BPHandle = 0x0000000D)
T393C 4729:966.169 - 0.036ms returns 0x00
T393C 4729:966.210 JLINK_HasError()
T393C 4729:966.254 JLINK_ClrBPEx(BPHandle = 0x0000000E)
T393C 4729:966.369 - 0.115ms returns 0x00
T393C 4729:966.411 JLINK_HasError()
T393C 4729:966.453 JLINK_ClrBPEx(BPHandle = 0x0000000F)
T393C 4729:966.487 - 0.034ms returns 0x00
T393C 4729:966.529 JLINK_HasError()
T393C 4729:966.571 JLINK_ClrBPEx(BPHandle = 0x00000010)
T393C 4729:966.605 - 0.034ms returns 0x00
T393C 4729:966.646 JLINK_HasError()
T393C 4729:966.687 JLINK_ClrBPEx(BPHandle = 0x00000011)
T393C 4729:966.724 - 0.036ms returns 0x00
T393C 4729:966.765 JLINK_HasError()
T393C 4729:966.809 JLINK_ClrBPEx(BPHandle = 0x00000012)
T393C 4729:966.844 - 0.034ms returns 0x00
T393C 4729:966.886 JLINK_HasError()
T393C 4729:966.929 JLINK_ClrBPEx(BPHandle = 0x00000013)
T393C 4729:966.963 - 0.034ms returns 0x00
T393C 4729:967.006 JLINK_HasError()
T393C 4729:967.049 JLINK_ClrBPEx(BPHandle = 0x00000014)
T393C 4729:967.084 - 0.035ms returns 0x00
T393C 4729:967.138 JLINK_HasError()
T393C 4729:967.211 JLINK_HasError()
T393C 4729:967.285 JLINK_ReadMemU32(0xE000ED30, 0x1 Items)
T393C 4729:967.341   CPU_ReadMem(4 bytes @ 0xE000ED30)
T393C 4729:968.426   Data:  02 00 00 00
T393C 4729:968.484 - 1.199ms returns 1 (0x1)
T393C 4729:968.535 JLINK_ReadMemU32(0xE0001028, 0x1 Items)
T393C 4729:968.578   CPU_ReadMem(4 bytes @ 0xE0001028)
T393C 4729:969.646   Data:  00 00 00 00
T393C 4729:969.704   Debug reg: DWT_FUNC[0]
T393C 4729:969.754 - 1.219ms returns 1 (0x1)
T393C 4729:969.802 JLINK_ReadMemU32(0xE0001038, 0x1 Items)
T393C 4729:969.842   CPU_ReadMem(4 bytes @ 0xE0001038)
T393C 4729:970.807   Data:  00 02 00 00
T393C 4729:970.863   Debug reg: DWT_FUNC[1]
T393C 4729:970.911 - 1.109ms returns 1 (0x1)
T393C 4729:970.961 JLINK_ReadMemU32(0xE0001048, 0x1 Items)
T393C 4729:970.996   CPU_ReadMem(4 bytes @ 0xE0001048)
T393C 4729:972.090   Data:  00 00 00 00
T393C 4729:972.142   Debug reg: DWT_FUNC[2]
T393C 4729:972.183 - 1.221ms returns 1 (0x1)
T393C 4729:972.222 JLINK_ReadMemU32(0xE0001058, 0x1 Items)
T393C 4729:972.255   CPU_ReadMem(4 bytes @ 0xE0001058)
T393C 4729:973.325   Data:  00 00 00 00
T393C 4729:973.371   Debug reg: DWT_FUNC[3]
T393C 4729:973.414 - 1.192ms returns 1 (0x1)
T393C 4729:973.566 JLINK_HasError()
T393C 4729:973.605 JLINK_ReadReg(R0)
T393C 4729:973.637 - 0.032ms returns 0x20005C08
T393C 4729:973.668 JLINK_ReadReg(R1)
T393C 4729:973.698 - 0.029ms returns 0x000309DC
T393C 4729:973.730 JLINK_ReadReg(R2)
T393C 4729:973.760 - 0.030ms returns 0xFFFFFFFF
T393C 4729:973.791 JLINK_ReadReg(R3)
T393C 4729:973.820 - 0.029ms returns 0x01D7E000
T393C 4729:973.853 JLINK_ReadReg(R4)
T393C 4729:973.884 - 0.030ms returns 0x000321DC
T393C 4729:973.917 JLINK_ReadReg(R5)
T393C 4729:973.948 - 0.031ms returns 0x000321DC
T393C 4729:973.982 JLINK_ReadReg(R6)
T393C 4729:974.015 - 0.032ms returns 0x10001000
T393C 4729:974.048 JLINK_ReadReg(R7)
T393C 4729:974.081 - 0.032ms returns 0x00000000
T393C 4729:974.116 JLINK_ReadReg(R8)
T393C 4729:974.148 - 0.031ms returns 0x00000000
T393C 4729:974.183 JLINK_ReadReg(R9)
T393C 4729:974.215 - 0.031ms returns 0x00000000
T393C 4729:974.252 JLINK_ReadReg(R10)
T393C 4729:974.316 - 0.063ms returns 0x00000000
T393C 4729:974.352 JLINK_ReadReg(R11)
T393C 4729:974.385 - 0.033ms returns 0x00000000
T393C 4729:974.420 JLINK_ReadReg(R12)
T393C 4729:974.452 - 0.031ms returns 0x00000000
T393C 4729:974.488 JLINK_ReadReg(R13 (SP))
T393C 4729:974.521 - 0.032ms returns 0x20005BF0
T393C 4729:974.556 JLINK_ReadReg(R14)
T393C 4729:974.588 - 0.032ms returns 0x0002B775
T393C 4729:974.623 JLINK_ReadReg(R15 (PC))
T393C 4729:974.654 - 0.031ms returns 0x0002B774
T393C 4729:974.687 JLINK_ReadReg(XPSR)
T393C 4729:974.719 - 0.031ms returns 0x81000000
T393C 4729:974.753 JLINK_ReadReg(MSP)
T393C 4729:974.784 - 0.030ms returns 0x20005BF0
T393C 4729:974.819 JLINK_ReadReg(PSP)
T393C 4729:974.851 - 0.031ms returns 0x00000000
T393C 4729:974.887 JLINK_ReadReg(CFBP)
T393C 4729:974.919 - 0.032ms returns 0x00000000
T393C 4729:974.955 JLINK_ReadReg(FPSCR)
T393C 4729:989.002 - 14.045ms returns 0x00000000
T393C 4729:989.089 JLINK_ReadReg(FPS0)
T393C 4729:989.127 - 0.038ms returns 0x00000000
T393C 4729:989.159 JLINK_ReadReg(FPS1)
T393C 4729:989.191 - 0.031ms returns 0x00000000
T393C 4729:989.223 JLINK_ReadReg(FPS2)
T393C 4729:989.253 - 0.029ms returns 0x00000000
T393C 4729:989.285 JLINK_ReadReg(FPS3)
T393C 4729:989.315 - 0.029ms returns 0x00000000
T393C 4729:989.347 JLINK_ReadReg(FPS4)
T393C 4729:989.376 - 0.029ms returns 0x00000000
T393C 4729:989.408 JLINK_ReadReg(FPS5)
T393C 4729:989.438 - 0.029ms returns 0x00000000
T393C 4729:989.470 JLINK_ReadReg(FPS6)
T393C 4729:989.500 - 0.029ms returns 0x00000000
T393C 4729:989.531 JLINK_ReadReg(FPS7)
T393C 4729:989.560 - 0.029ms returns 0x00000000
T393C 4729:989.592 JLINK_ReadReg(FPS8)
T393C 4729:989.621 - 0.028ms returns 0x00000000
T393C 4729:989.652 JLINK_ReadReg(FPS9)
T393C 4729:989.681 - 0.029ms returns 0x00000000
T393C 4729:989.714 JLINK_ReadReg(FPS10)
T393C 4729:989.753 - 0.039ms returns 0x00000000
T393C 4729:989.796 JLINK_ReadReg(FPS11)
T393C 4729:989.832 - 0.035ms returns 0x00000000
T393C 4729:989.865 JLINK_ReadReg(FPS12)
T393C 4729:989.895 - 0.030ms returns 0x00000000
T393C 4729:989.928 JLINK_ReadReg(FPS13)
T393C 4729:989.957 - 0.028ms returns 0x00000000
T393C 4729:989.990 JLINK_ReadReg(FPS14)
T393C 4729:990.022 - 0.031ms returns 0x00000000
T393C 4729:990.058 JLINK_ReadReg(FPS15)
T393C 4729:990.090 - 0.031ms returns 0x00000000
T393C 4729:990.123 JLINK_ReadReg(FPS16)
T393C 4729:990.156 - 0.031ms returns 0x00000000
T393C 4729:990.190 JLINK_ReadReg(FPS17)
T393C 4729:990.222 - 0.031ms returns 0x00000000
T393C 4729:990.256 JLINK_ReadReg(FPS18)
T393C 4729:990.290 - 0.033ms returns 0x00000000
T393C 4729:990.324 JLINK_ReadReg(FPS19)
T393C 4729:990.355 - 0.030ms returns 0x00000000
T393C 4729:990.392 JLINK_ReadReg(FPS20)
T393C 4729:990.423 - 0.031ms returns 0x00000000
T393C 4729:990.458 JLINK_ReadReg(FPS21)
T393C 4729:990.490 - 0.032ms returns 0x00000000
T393C 4729:990.524 JLINK_ReadReg(FPS22)
T393C 4729:990.558 - 0.033ms returns 0x00000000
T393C 4729:990.592 JLINK_ReadReg(FPS23)
T393C 4729:990.625 - 0.032ms returns 0x00000000
T393C 4729:990.658 JLINK_ReadReg(FPS24)
T393C 4729:990.690 - 0.031ms returns 0x00000000
T393C 4729:990.752 JLINK_ReadReg(FPS25)
T393C 4729:990.784 - 0.031ms returns 0x00000000
T393C 4729:990.818 JLINK_ReadReg(FPS26)
T393C 4729:990.850 - 0.031ms returns 0x00000000
T393C 4729:990.883 JLINK_ReadReg(FPS27)
T393C 4729:990.915 - 0.031ms returns 0x00000000
T393C 4729:990.949 JLINK_ReadReg(FPS28)
T393C 4729:990.981 - 0.031ms returns 0x00000000
T393C 4729:991.015 JLINK_ReadReg(FPS29)
T393C 4729:991.044 - 0.029ms returns 0x00000000
T393C 4729:991.076 JLINK_ReadReg(FPS30)
T393C 4729:991.104 - 0.028ms returns 0x00000000
T393C 4729:991.136 JLINK_ReadReg(FPS31)
T393C 4729:991.166 - 0.029ms returns 0x00000000
T3964 4729:991.688 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T3964 4729:991.863   CPU_ReadMem(64 bytes @ 0x00000000)
T3964 4729:994.017    -- Updating C cache (64 bytes @ 0x00000000)
T3964 4729:994.085    -- Read from C cache (4 bytes @ 0x00000000)
T3964 4729:994.133   Data:  00 04 00 20
T3964 4729:994.178 - 2.491ms returns 4 (0x4)
T3964 4729:994.254 JLINK_HasError()
T3964 4729:994.288 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T3964 4729:994.338   CPU_ReadMem(4 bytes @ 0xE0001004)
T3964 4729:995.373   Data:  35 8B 87 01
T3964 4729:995.439   Debug reg: DWT_CYCCNT
T3964 4729:995.489 - 1.201ms returns 1 (0x1)
T393C 4730:364.636 JLINK_ReadMemEx(0x0002B774, 0x2 Bytes, Flags = 0x02000000)
T393C 4730:364.717    -- Merging zombie BP[2]: 0x4817 @ 0x0002B774
T393C 4730:364.751    -- Merging zombie BP[2]: 0x4817 @ 0x0002B774
T393C 4730:364.783   Data:  17 48
T393C 4730:364.816 - 0.180ms returns 2 (0x2)
T393C 4730:364.839 JLINK_HasError()
T393C 4730:364.865 JLINK_Step()
T393C 4730:364.893    -- Merging zombie BP[2]: 0x4817 @ 0x0002B774
T393C 4730:364.924    -- Merging zombie BP[2]: 0x4817 @ 0x0002B774
T393C 4730:364.962    -- Read from flash cache (4 bytes @ 0x0002B7D4)
T393C 4730:364.994   -- Simulated
T393C 4730:365.026 - 0.160ms returns 0
T393C 4730:365.048 JLINK_HasError()
T393C 4730:365.071 JLINK_ReadReg(R15 (PC))
T393C 4730:365.094 - 0.022ms returns 0x0002B776
T393C 4730:365.117 JLINK_ReadReg(XPSR)
T393C 4730:365.139 - 0.021ms returns 0x81000000
T393C 4730:365.167 JLINK_HasError()
T393C 4730:365.191 JLINK_SetBPEx(Addr = 0x0002B768, Type = 0xFFFFFFF2)
T393C 4730:365.214 - 0.023ms returns 0x00000015
T393C 4730:365.237 JLINK_HasError()
T393C 4730:365.260 JLINK_SetBPEx(Addr = 0x0002B790, Type = 0xFFFFFFF2)
T393C 4730:365.282 - 0.021ms returns 0x00000016
T393C 4730:365.303 JLINK_HasError()
T393C 4730:365.326 JLINK_SetBPEx(Addr = 0x0002B774, Type = 0xFFFFFFF2)
T393C 4730:365.348 - 0.021ms returns 0x00000017
T393C 4730:365.370 JLINK_HasError()
T393C 4730:365.392 JLINK_SetBPEx(Addr = 0x0002B76A, Type = 0xFFFFFFF2)
T393C 4730:365.413 - 0.021ms returns 0x00000018
T393C 4730:365.435 JLINK_HasError()
T393C 4730:365.458 JLINK_SetBPEx(Addr = 0x00029C20, Type = 0xFFFFFFF2)
T393C 4730:365.493 - 0.034ms returns 0x00000019
T393C 4730:365.516 JLINK_HasError()
T393C 4730:365.540 JLINK_SetBPEx(Addr = 0x00029C00, Type = 0xFFFFFFF2)
T393C 4730:365.562 - 0.022ms returns 0x0000001A
T393C 4730:365.584 JLINK_HasError()
T393C 4730:365.607 JLINK_SetBPEx(Addr = 0x0002F7C4, Type = 0xFFFFFFF2)
T393C 4730:365.629 - 0.022ms returns 0x0000001B
T393C 4730:365.651 JLINK_HasError()
T393C 4730:365.673 JLINK_SetBPEx(Addr = 0x0002F6F0, Type = 0xFFFFFFF2)
T393C 4730:365.695 - 0.021ms returns 0x0000001C
T393C 4730:365.717 JLINK_HasError()
T393C 4730:365.739 JLINK_SetBPEx(Addr = 0x00029C6E, Type = 0xFFFFFFF2)
T393C 4730:365.760 - 0.021ms returns 0x0000001D
T393C 4730:365.783 JLINK_HasError()
T393C 4730:365.806 JLINK_SetBPEx(Addr = 0x00029C68, Type = 0xFFFFFFF2)
T393C 4730:365.828 - 0.022ms returns 0x0000001E
T393C 4730:365.850 JLINK_HasError()
T393C 4730:365.872 JLINK_HasError()
T393C 4730:365.916 JLINK_Go()
T393C 4730:365.946   CPU_WriteMem(4 bytes @ 0x********)
T393C 4730:366.933   CPU_WriteMem(4 bytes @ 0x********)
T393C 4730:367.918   CPU_WriteMem(4 bytes @ 0x********)
T393C 4730:368.890   CPU_WriteMem(4 bytes @ 0x********)
T393C 4730:369.907   CPU_WriteMem(4 bytes @ 0x********)
T393C 4730:370.908   CPU_WriteMem(4 bytes @ 0x********)
T393C 4730:371.914   CPU_ReadMem(4 bytes @ 0xE0001000)
T393C 4730:372.934   CPU_WriteMem(4 bytes @ 0xE0001004)
T393C 4730:376.915 - 10.998ms
T393C 4730:477.615 JLINK_HasError()
T393C 4730:477.720 JLINK_IsHalted()
T393C 4730:487.045 - 9.322ms returns TRUE
T393C 4730:487.183 JLINK_HasError()
T393C 4730:487.236 JLINK_Halt()
T393C 4730:487.276 - 0.038ms returns 0x00
T393C 4730:487.324 JLINK_IsHalted()
T393C 4730:487.362 - 0.038ms returns TRUE
T393C 4730:487.408 JLINK_IsHalted()
T393C 4730:487.445 - 0.036ms returns TRUE
T393C 4730:487.492 JLINK_IsHalted()
T393C 4730:487.528 - 0.036ms returns TRUE
T393C 4730:487.573 JLINK_HasError()
T393C 4730:487.623 JLINK_ReadReg(R15 (PC))
T393C 4730:487.667 - 0.043ms returns 0x0002B790
T393C 4730:487.712 JLINK_ReadReg(XPSR)
T393C 4730:487.750 - 0.037ms returns 0x21000000
T393C 4730:487.906 JLINK_HasError()
T393C 4730:487.950 JLINK_ClrBPEx(BPHandle = 0x00000015)
T393C 4730:487.987 - 0.037ms returns 0x00
T393C 4730:488.027 JLINK_HasError()
T393C 4730:488.062 JLINK_ClrBPEx(BPHandle = 0x00000016)
T393C 4730:488.094 - 0.032ms returns 0x00
T393C 4730:488.128 JLINK_HasError()
T393C 4730:488.161 JLINK_ClrBPEx(BPHandle = 0x00000017)
T393C 4730:488.193 - 0.031ms returns 0x00
T393C 4730:488.228 JLINK_HasError()
T393C 4730:488.261 JLINK_ClrBPEx(BPHandle = 0x00000018)
T393C 4730:488.293 - 0.031ms returns 0x00
T393C 4730:488.327 JLINK_HasError()
T393C 4730:488.361 JLINK_ClrBPEx(BPHandle = 0x00000019)
T393C 4730:488.394 - 0.033ms returns 0x00
T393C 4730:488.430 JLINK_HasError()
T393C 4730:488.464 JLINK_ClrBPEx(BPHandle = 0x0000001A)
T393C 4730:488.496 - 0.032ms returns 0x00
T393C 4730:488.531 JLINK_HasError()
T393C 4730:488.564 JLINK_ClrBPEx(BPHandle = 0x0000001B)
T393C 4730:488.597 - 0.033ms returns 0x00
T393C 4730:488.632 JLINK_HasError()
T393C 4730:488.665 JLINK_ClrBPEx(BPHandle = 0x0000001C)
T393C 4730:488.698 - 0.032ms returns 0x00
T393C 4730:488.734 JLINK_HasError()
T393C 4730:488.767 JLINK_ClrBPEx(BPHandle = 0x0000001D)
T393C 4730:488.800 - 0.032ms returns 0x00
T393C 4730:488.834 JLINK_HasError()
T393C 4730:488.867 JLINK_ClrBPEx(BPHandle = 0x0000001E)
T393C 4730:488.899 - 0.031ms returns 0x00
T393C 4730:488.933 JLINK_HasError()
T393C 4730:488.964 JLINK_HasError()
T393C 4730:488.996 JLINK_ReadMemU32(0xE000ED30, 0x1 Items)
T393C 4730:489.037   CPU_ReadMem(4 bytes @ 0xE000ED30)
T393C 4730:490.145   Data:  02 00 00 00
T393C 4730:490.193 - 1.196ms returns 1 (0x1)
T393C 4730:490.229 JLINK_ReadMemU32(0xE0001028, 0x1 Items)
T393C 4730:490.262   CPU_ReadMem(4 bytes @ 0xE0001028)
T393C 4730:491.305   Data:  00 00 00 00
T393C 4730:491.357   Debug reg: DWT_FUNC[0]
T393C 4730:491.404 - 1.175ms returns 1 (0x1)
T393C 4730:491.439 JLINK_ReadMemU32(0xE0001038, 0x1 Items)
T393C 4730:491.487   CPU_ReadMem(4 bytes @ 0xE0001038)
T393C 4730:492.556   Data:  00 02 00 00
T393C 4730:492.620   Debug reg: DWT_FUNC[1]
T393C 4730:492.672 - 1.232ms returns 1 (0x1)
T393C 4730:492.712 JLINK_ReadMemU32(0xE0001048, 0x1 Items)
T393C 4730:492.751   CPU_ReadMem(4 bytes @ 0xE0001048)
T393C 4730:493.799   Data:  00 00 00 00
T393C 4730:493.856   Debug reg: DWT_FUNC[2]
T393C 4730:493.908 - 1.195ms returns 1 (0x1)
T393C 4730:493.948 JLINK_ReadMemU32(0xE0001058, 0x1 Items)
T393C 4730:493.985   CPU_ReadMem(4 bytes @ 0xE0001058)
T393C 4730:494.981   Data:  00 00 00 00
T393C 4730:495.053   Debug reg: DWT_FUNC[3]
T393C 4730:495.109 - 1.160ms returns 1 (0x1)
T393C 4730:495.226 JLINK_HasError()
T393C 4730:495.276 JLINK_ReadReg(R0)
T393C 4730:495.329 - 0.052ms returns 0x00000000
T393C 4730:495.380 JLINK_ReadReg(R1)
T393C 4730:495.428 - 0.047ms returns 0x00000000
T393C 4730:495.467 JLINK_ReadReg(R2)
T393C 4730:495.501 - 0.033ms returns 0x00000000
T393C 4730:495.537 JLINK_ReadReg(R3)
T393C 4730:495.574 - 0.036ms returns 0x00000000
T393C 4730:495.610 JLINK_ReadReg(R4)
T393C 4730:495.645 - 0.034ms returns 0x000321DC
T393C 4730:495.681 JLINK_ReadReg(R5)
T393C 4730:495.717 - 0.035ms returns 0x000321DC
T393C 4730:495.755 JLINK_ReadReg(R6)
T393C 4730:495.789 - 0.033ms returns 0x10001000
T393C 4730:495.826 JLINK_ReadReg(R7)
T393C 4730:495.860 - 0.033ms returns 0x00000000
T393C 4730:495.897 JLINK_ReadReg(R8)
T393C 4730:495.931 - 0.034ms returns 0x00000000
T393C 4730:495.968 JLINK_ReadReg(R9)
T393C 4730:496.003 - 0.034ms returns 0x00000000
T393C 4730:496.040 JLINK_ReadReg(R10)
T393C 4730:496.071 - 0.031ms returns 0x00000000
T393C 4730:496.106 JLINK_ReadReg(R11)
T393C 4730:496.137 - 0.031ms returns 0x00000000
T393C 4730:496.171 JLINK_ReadReg(R12)
T393C 4730:496.203 - 0.031ms returns 0x00000000
T393C 4730:496.237 JLINK_ReadReg(R13 (SP))
T393C 4730:496.270 - 0.033ms returns 0x20005BF0
T393C 4730:496.305 JLINK_ReadReg(R14)
T393C 4730:496.336 - 0.031ms returns 0x0002D93D
T393C 4730:496.370 JLINK_ReadReg(R15 (PC))
T393C 4730:496.402 - 0.032ms returns 0x0002B790
T393C 4730:496.435 JLINK_ReadReg(XPSR)
T393C 4730:496.467 - 0.031ms returns 0x21000000
T393C 4730:496.501 JLINK_ReadReg(MSP)
T393C 4730:496.534 - 0.032ms returns 0x20005BF0
T393C 4730:496.569 JLINK_ReadReg(PSP)
T393C 4730:496.600 - 0.030ms returns 0x00000000
T393C 4730:496.634 JLINK_ReadReg(CFBP)
T393C 4730:496.666 - 0.031ms returns 0x00000000
T393C 4730:496.718 JLINK_ReadReg(FPSCR)
T393C 4730:510.818 - 14.097ms returns 0x00000000
T393C 4730:510.934 JLINK_ReadReg(FPS0)
T393C 4730:510.986 - 0.052ms returns 0x00000000
T393C 4730:511.029 JLINK_ReadReg(FPS1)
T393C 4730:511.077 - 0.047ms returns 0x00000000
T393C 4730:511.120 JLINK_ReadReg(FPS2)
T393C 4730:511.160 - 0.039ms returns 0x00000000
T393C 4730:511.202 JLINK_ReadReg(FPS3)
T393C 4730:511.246 - 0.043ms returns 0x00000000
T393C 4730:511.292 JLINK_ReadReg(FPS4)
T393C 4730:511.335 - 0.042ms returns 0x00000000
T393C 4730:511.398 JLINK_ReadReg(FPS5)
T393C 4730:511.443 - 0.045ms returns 0x00000000
T393C 4730:511.487 JLINK_ReadReg(FPS6)
T393C 4730:511.528 - 0.041ms returns 0x00000000
T393C 4730:511.574 JLINK_ReadReg(FPS7)
T393C 4730:511.616 - 0.041ms returns 0x00000000
T393C 4730:511.661 JLINK_ReadReg(FPS8)
T393C 4730:511.702 - 0.041ms returns 0x00000000
T393C 4730:511.749 JLINK_ReadReg(FPS9)
T393C 4730:511.793 - 0.042ms returns 0x00000000
T393C 4730:511.837 JLINK_ReadReg(FPS10)
T393C 4730:511.879 - 0.042ms returns 0x00000000
T393C 4730:511.924 JLINK_ReadReg(FPS11)
T393C 4730:511.968 - 0.043ms returns 0x00000000
T393C 4730:512.008 JLINK_ReadReg(FPS12)
T393C 4730:512.048 - 0.039ms returns 0x00000000
T393C 4730:512.088 JLINK_ReadReg(FPS13)
T393C 4730:512.129 - 0.039ms returns 0x00000000
T393C 4730:512.170 JLINK_ReadReg(FPS14)
T393C 4730:512.207 - 0.037ms returns 0x00000000
T393C 4730:512.249 JLINK_ReadReg(FPS15)
T393C 4730:512.286 - 0.036ms returns 0x00000000
T393C 4730:512.325 JLINK_ReadReg(FPS16)
T393C 4730:512.364 - 0.038ms returns 0x00000000
T393C 4730:512.594 JLINK_ReadReg(FPS17)
T393C 4730:512.685 - 0.091ms returns 0x00000000
T393C 4730:512.733 JLINK_ReadReg(FPS18)
T393C 4730:512.773 - 0.040ms returns 0x00000000
T393C 4730:512.815 JLINK_ReadReg(FPS19)
T393C 4730:512.854 - 0.038ms returns 0x00000000
T393C 4730:512.895 JLINK_ReadReg(FPS20)
T393C 4730:512.934 - 0.038ms returns 0x00000000
T393C 4730:512.973 JLINK_ReadReg(FPS21)
T393C 4730:513.007 - 0.034ms returns 0x00000000
T393C 4730:513.044 JLINK_ReadReg(FPS22)
T393C 4730:513.078 - 0.034ms returns 0x00000000
T393C 4730:513.115 JLINK_ReadReg(FPS23)
T393C 4730:513.149 - 0.033ms returns 0x00000000
T393C 4730:513.185 JLINK_ReadReg(FPS24)
T393C 4730:513.220 - 0.034ms returns 0x00000000
T393C 4730:513.256 JLINK_ReadReg(FPS25)
T393C 4730:513.290 - 0.033ms returns 0x00000000
T393C 4730:513.328 JLINK_ReadReg(FPS26)
T393C 4730:513.362 - 0.033ms returns 0x00000000
T393C 4730:513.400 JLINK_ReadReg(FPS27)
T393C 4730:513.436 - 0.035ms returns 0x00000000
T393C 4730:513.476 JLINK_ReadReg(FPS28)
T393C 4730:513.522 - 0.045ms returns 0x00000000
T393C 4730:513.561 JLINK_ReadReg(FPS29)
T393C 4730:513.596 - 0.034ms returns 0x00000000
T393C 4730:513.633 JLINK_ReadReg(FPS30)
T393C 4730:513.668 - 0.035ms returns 0x00000000
T393C 4730:513.705 JLINK_ReadReg(FPS31)
T393C 4730:513.742 - 0.036ms returns 0x00000000
T3964 4730:514.294 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T3964 4730:514.376   CPU_ReadMem(64 bytes @ 0x00000000)
T3964 4730:516.685    -- Updating C cache (64 bytes @ 0x00000000)
T3964 4730:516.732    -- Read from C cache (4 bytes @ 0x00000000)
T3964 4730:516.777   Data:  00 04 00 20
T3964 4730:516.819 - 2.526ms returns 4 (0x4)
T3964 4730:516.876 JLINK_HasError()
T3964 4730:516.907 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T3964 4730:516.942   CPU_ReadMem(4 bytes @ 0xE0001004)
T3964 4730:517.976   Data:  4A 91 87 01
T3964 4730:518.024   Debug reg: DWT_CYCCNT
T3964 4730:518.068 - 1.160ms returns 1 (0x1)
T3964 4730:522.189 JLINK_ReadMemEx(0x0002B790, 0x3C Bytes, Flags = 0x02000000)
T3964 4730:522.289    -- Read from flash cache (60 bytes @ 0x0002B790)
T3964 4730:522.348    -- Merging zombie BP[1]: 0xB108 @ 0x0002B790
T3964 4730:522.405   Data:  08 B1 FC F7 E7 FA 4F F4 80 41 00 20 01 F0 74 FA ...
T3964 4730:522.462 - 0.274ms returns 60 (0x3C)
T393C 4730:908.591 JLINK_ReadMemEx(0x0002B790, 0x2 Bytes, Flags = 0x02000000)
T393C 4730:908.669    -- Merging zombie BP[1]: 0xB108 @ 0x0002B790
T393C 4730:908.701    -- Merging zombie BP[1]: 0xB108 @ 0x0002B790
T393C 4730:908.732   Data:  08 B1
T393C 4730:908.764 - 0.173ms returns 2 (0x2)
T393C 4730:908.788 JLINK_HasError()
T393C 4730:908.814 JLINK_Step()
T393C 4730:908.840    -- Merging zombie BP[1]: 0xB108 @ 0x0002B790
T393C 4730:908.870    -- Merging zombie BP[1]: 0xB108 @ 0x0002B790
T393C 4730:908.905   -- Simulated
T393C 4730:908.936 - 0.121ms returns 0
T393C 4730:908.958 JLINK_HasError()
T393C 4730:908.982 JLINK_ReadReg(R15 (PC))
T393C 4730:909.004 - 0.022ms returns 0x0002B796
T393C 4730:909.027 JLINK_ReadReg(XPSR)
T393C 4730:909.048 - 0.021ms returns 0x21000000
T393C 4730:909.075 JLINK_HasError()
T393C 4730:909.098 JLINK_SetBPEx(Addr = 0x0002B768, Type = 0xFFFFFFF2)
T393C 4730:909.120 - 0.022ms returns 0x0000001F
T393C 4730:909.142 JLINK_HasError()
T393C 4730:909.164 JLINK_SetBPEx(Addr = 0x0002B790, Type = 0xFFFFFFF2)
T393C 4730:909.185 - 0.021ms returns 0x00000020
T393C 4730:909.207 JLINK_HasError()
T393C 4730:909.229 JLINK_SetBPEx(Addr = 0x0002B774, Type = 0xFFFFFFF2)
T393C 4730:909.250 - 0.021ms returns 0x00000021
T393C 4730:909.271 JLINK_HasError()
T393C 4730:909.293 JLINK_SetBPEx(Addr = 0x0002B76A, Type = 0xFFFFFFF2)
T393C 4730:909.314 - 0.020ms returns 0x00000022
T393C 4730:909.336 JLINK_HasError()
T393C 4730:909.357 JLINK_SetBPEx(Addr = 0x00029C20, Type = 0xFFFFFFF2)
T393C 4730:909.379 - 0.021ms returns 0x00000023
T393C 4730:909.400 JLINK_HasError()
T393C 4730:909.422 JLINK_SetBPEx(Addr = 0x00029C00, Type = 0xFFFFFFF2)
T393C 4730:909.443 - 0.021ms returns 0x00000024
T393C 4730:909.474 JLINK_HasError()
T393C 4730:909.500 JLINK_SetBPEx(Addr = 0x0002F7C4, Type = 0xFFFFFFF2)
T393C 4730:909.521 - 0.021ms returns 0x00000025
T393C 4730:909.543 JLINK_HasError()
T393C 4730:909.565 JLINK_SetBPEx(Addr = 0x0002F6F0, Type = 0xFFFFFFF2)
T393C 4730:909.586 - 0.020ms returns 0x00000026
T393C 4730:909.608 JLINK_HasError()
T393C 4730:909.629 JLINK_SetBPEx(Addr = 0x00029C6E, Type = 0xFFFFFFF2)
T393C 4730:909.651 - 0.021ms returns 0x00000027
T393C 4730:909.672 JLINK_HasError()
T393C 4730:909.694 JLINK_SetBPEx(Addr = 0x00029C68, Type = 0xFFFFFFF2)
T393C 4730:909.715 - 0.020ms returns 0x00000028
T393C 4730:909.737 JLINK_HasError()
T393C 4730:909.759 JLINK_HasError()
T393C 4730:909.781 JLINK_Go()
T393C 4730:909.810   CPU_WriteMem(4 bytes @ 0x********)
T393C 4730:910.783   CPU_WriteMem(4 bytes @ 0x********)
T393C 4730:911.825   CPU_WriteMem(4 bytes @ 0x********)
T393C 4730:912.901   CPU_WriteMem(4 bytes @ 0x********)
T393C 4730:913.914   CPU_WriteMem(4 bytes @ 0x********)
T393C 4730:914.992   CPU_WriteMem(4 bytes @ 0x********)
T393C 4730:915.944   CPU_ReadMem(4 bytes @ 0xE0001000)
T393C 4730:916.940   CPU_WriteMem(4 bytes @ 0xE0001004)
T393C 4730:920.512 - 10.730ms
T393C 4731:021.454 JLINK_HasError()
T393C 4731:021.539 JLINK_IsHalted()
T393C 4731:022.748 - 1.207ms returns FALSE
T393C 4731:124.078 JLINK_HasError()
T393C 4731:124.166 JLINK_IsHalted()
T393C 4731:125.177 - 1.010ms returns FALSE
T393C 4731:225.788 JLINK_HasError()
T393C 4731:225.880 JLINK_IsHalted()
T393C 4731:226.973 - 1.091ms returns FALSE
T393C 4731:328.078 JLINK_HasError()
T393C 4731:328.251 JLINK_IsHalted()
T393C 4731:329.619 - 1.366ms returns FALSE
T393C 4731:431.111 JLINK_HasError()
T393C 4731:431.150 JLINK_IsHalted()
T393C 4731:432.237 - 1.086ms returns FALSE
T393C 4731:533.273 JLINK_HasError()
T393C 4731:533.386 JLINK_IsHalted()
T393C 4731:534.444 - 1.055ms returns FALSE
T393C 4731:635.021 JLINK_HasError()
T393C 4731:635.131 JLINK_IsHalted()
T393C 4731:644.599 - 9.466ms returns TRUE
T393C 4731:644.702 JLINK_HasError()
T393C 4731:644.738 JLINK_Halt()
T393C 4731:644.771 - 0.032ms returns 0x00
T393C 4731:644.804 JLINK_IsHalted()
T393C 4731:644.835 - 0.030ms returns TRUE
T393C 4731:644.869 JLINK_IsHalted()
T393C 4731:644.899 - 0.030ms returns TRUE
T393C 4731:644.931 JLINK_IsHalted()
T393C 4731:644.962 - 0.030ms returns TRUE
T393C 4731:644.994 JLINK_HasError()
T393C 4731:645.027 JLINK_ReadReg(R15 (PC))
T393C 4731:645.063 - 0.035ms returns 0x00027D0C
T393C 4731:645.095 JLINK_ReadReg(XPSR)
T393C 4731:645.125 - 0.030ms returns 0x81000000
T393C 4731:645.165 JLINK_HasError()
T393C 4731:645.197 JLINK_ClrBPEx(BPHandle = 0x0000001F)
T393C 4731:645.228 - 0.031ms returns 0x00
T393C 4731:645.260 JLINK_HasError()
T393C 4731:645.292 JLINK_ClrBPEx(BPHandle = 0x00000020)
T393C 4731:645.323 - 0.030ms returns 0x00
T393C 4731:645.355 JLINK_HasError()
T393C 4731:645.387 JLINK_ClrBPEx(BPHandle = 0x00000021)
T393C 4731:645.416 - 0.029ms returns 0x00
T393C 4731:645.447 JLINK_HasError()
T393C 4731:645.478 JLINK_ClrBPEx(BPHandle = 0x00000022)
T393C 4731:645.509 - 0.030ms returns 0x00
T393C 4731:645.540 JLINK_HasError()
T393C 4731:645.572 JLINK_ClrBPEx(BPHandle = 0x00000023)
T393C 4731:645.603 - 0.031ms returns 0x00
T393C 4731:645.637 JLINK_HasError()
T393C 4731:645.669 JLINK_ClrBPEx(BPHandle = 0x00000024)
T393C 4731:645.699 - 0.029ms returns 0x00
T393C 4731:645.731 JLINK_HasError()
T393C 4731:645.762 JLINK_ClrBPEx(BPHandle = 0x00000025)
T393C 4731:645.792 - 0.029ms returns 0x00
T393C 4731:645.823 JLINK_HasError()
T393C 4731:645.854 JLINK_ClrBPEx(BPHandle = 0x00000026)
T393C 4731:645.885 - 0.031ms returns 0x00
T393C 4731:645.917 JLINK_HasError()
T393C 4731:645.948 JLINK_ClrBPEx(BPHandle = 0x00000027)
T393C 4731:645.977 - 0.029ms returns 0x00
T393C 4731:646.009 JLINK_HasError()
T393C 4731:646.040 JLINK_ClrBPEx(BPHandle = 0x00000028)
T393C 4731:646.069 - 0.029ms returns 0x00
T393C 4731:646.102 JLINK_HasError()
T393C 4731:646.131 JLINK_HasError()
T393C 4731:646.161 JLINK_ReadMemU32(0xE000ED30, 0x1 Items)
T393C 4731:646.214   CPU_ReadMem(4 bytes @ 0xE000ED30)
T393C 4731:647.241   Data:  02 00 00 00
T393C 4731:647.286 - 1.126ms returns 1 (0x1)
T393C 4731:647.319 JLINK_ReadMemU32(0xE0001028, 0x1 Items)
T393C 4731:647.351   CPU_ReadMem(4 bytes @ 0xE0001028)
T393C 4731:648.335   Data:  00 00 00 00
T393C 4731:648.385   Debug reg: DWT_FUNC[0]
T393C 4731:648.430 - 1.111ms returns 1 (0x1)
T393C 4731:648.464 JLINK_ReadMemU32(0xE0001038, 0x1 Items)
T393C 4731:648.497   CPU_ReadMem(4 bytes @ 0xE0001038)
T393C 4731:649.576   Data:  00 02 00 00
T393C 4731:649.640   Debug reg: DWT_FUNC[1]
T393C 4731:649.697 - 1.232ms returns 1 (0x1)
T393C 4731:649.746 JLINK_ReadMemU32(0xE0001048, 0x1 Items)
T393C 4731:649.792   CPU_ReadMem(4 bytes @ 0xE0001048)
T393C 4731:650.799   Data:  00 00 00 00
T393C 4731:650.858   Debug reg: DWT_FUNC[2]
T393C 4731:650.912 - 1.166ms returns 1 (0x1)
T393C 4731:650.954 JLINK_ReadMemU32(0xE0001058, 0x1 Items)
T393C 4731:650.993   CPU_ReadMem(4 bytes @ 0xE0001058)
T393C 4731:652.251   Data:  00 00 00 00
T393C 4731:652.346   Debug reg: DWT_FUNC[3]
T393C 4731:652.390 - 1.435ms returns 1 (0x1)
T393C 4731:652.508 JLINK_HasError()
T393C 4731:652.546 JLINK_ReadReg(R0)
T393C 4731:652.583 - 0.036ms returns 0x80000000
T393C 4731:652.615 JLINK_ReadReg(R1)
T393C 4731:652.646 - 0.030ms returns 0x0000000E
T393C 4731:652.678 JLINK_ReadReg(R2)
T393C 4731:652.709 - 0.031ms returns 0x00000000
T393C 4731:652.741 JLINK_ReadReg(R3)
T393C 4731:652.773 - 0.031ms returns 0x0000000C
T393C 4731:652.807 JLINK_ReadReg(R4)
T393C 4731:652.844 - 0.037ms returns 0x00000016
T393C 4731:652.877 JLINK_ReadReg(R5)
T393C 4731:652.909 - 0.031ms returns 0x000321DC
T393C 4731:652.942 JLINK_ReadReg(R6)
T393C 4731:652.971 - 0.028ms returns 0x10001000
T393C 4731:653.002 JLINK_ReadReg(R7)
T393C 4731:653.032 - 0.029ms returns 0x00000000
T393C 4731:653.062 JLINK_ReadReg(R8)
T393C 4731:653.091 - 0.029ms returns 0x00000000
T393C 4731:653.122 JLINK_ReadReg(R9)
T393C 4731:653.152 - 0.029ms returns 0x00000000
T393C 4731:653.182 JLINK_ReadReg(R10)
T393C 4731:653.211 - 0.028ms returns 0x00000000
T393C 4731:653.243 JLINK_ReadReg(R11)
T393C 4731:653.272 - 0.028ms returns 0x00000000
T393C 4731:653.301 JLINK_ReadReg(R12)
T393C 4731:653.328 - 0.027ms returns 0x00000000
T393C 4731:653.357 JLINK_ReadReg(R13 (SP))
T393C 4731:653.385 - 0.028ms returns 0x20005B28
T393C 4731:653.415 JLINK_ReadReg(R14)
T393C 4731:653.442 - 0.027ms returns 0x0002FD9D
T393C 4731:653.471 JLINK_ReadReg(R15 (PC))
T393C 4731:653.499 - 0.027ms returns 0x00027D0C
T393C 4731:653.528 JLINK_ReadReg(XPSR)
T393C 4731:653.556 - 0.027ms returns 0x81000000
T393C 4731:653.585 JLINK_ReadReg(MSP)
T393C 4731:653.613 - 0.027ms returns 0x20005B28
T393C 4731:653.642 JLINK_ReadReg(PSP)
T393C 4731:653.670 - 0.027ms returns 0x00000000
T393C 4731:653.700 JLINK_ReadReg(CFBP)
T393C 4731:653.727 - 0.027ms returns 0x00000001
T393C 4731:653.757 JLINK_ReadReg(FPSCR)
T393C 4731:668.021 - 14.262ms returns 0x00000000
T393C 4731:668.143 JLINK_ReadReg(FPS0)
T393C 4731:668.181 - 0.038ms returns 0x00000000
T393C 4731:668.219 JLINK_ReadReg(FPS1)
T393C 4731:668.251 - 0.031ms returns 0x00000000
T393C 4731:668.288 JLINK_ReadReg(FPS2)
T393C 4731:668.321 - 0.032ms returns 0x00000000
T393C 4731:668.359 JLINK_ReadReg(FPS3)
T393C 4731:668.390 - 0.030ms returns 0x00000000
T393C 4731:668.427 JLINK_ReadReg(FPS4)
T393C 4731:668.458 - 0.031ms returns 0x00000000
T393C 4731:668.490 JLINK_ReadReg(FPS5)
T393C 4731:668.520 - 0.030ms returns 0x00000000
T393C 4731:668.562 JLINK_ReadReg(FPS6)
T393C 4731:668.594 - 0.032ms returns 0x00000000
T393C 4731:668.633 JLINK_ReadReg(FPS7)
T393C 4731:668.665 - 0.031ms returns 0x00000000
T393C 4731:668.703 JLINK_ReadReg(FPS8)
T393C 4731:668.734 - 0.031ms returns 0x00000000
T393C 4731:668.773 JLINK_ReadReg(FPS9)
T393C 4731:668.805 - 0.031ms returns 0x00000000
T393C 4731:668.843 JLINK_ReadReg(FPS10)
T393C 4731:668.874 - 0.031ms returns 0x00000000
T393C 4731:668.912 JLINK_ReadReg(FPS11)
T393C 4731:668.943 - 0.031ms returns 0x00000000
T393C 4731:669.004 JLINK_ReadReg(FPS12)
T393C 4731:669.039 - 0.034ms returns 0x00000000
T393C 4731:669.081 JLINK_ReadReg(FPS13)
T393C 4731:669.115 - 0.033ms returns 0x00000000
T393C 4731:669.156 JLINK_ReadReg(FPS14)
T393C 4731:669.191 - 0.034ms returns 0x00000000
T393C 4731:669.232 JLINK_ReadReg(FPS15)
T393C 4731:669.265 - 0.032ms returns 0x00000000
T393C 4731:669.306 JLINK_ReadReg(FPS16)
T393C 4731:669.341 - 0.034ms returns 0x00000000
T393C 4731:669.381 JLINK_ReadReg(FPS17)
T393C 4731:669.416 - 0.034ms returns 0x00000000
T393C 4731:669.457 JLINK_ReadReg(FPS18)
T393C 4731:669.491 - 0.033ms returns 0x00000000
T393C 4731:669.533 JLINK_ReadReg(FPS19)
T393C 4731:669.567 - 0.033ms returns 0x00000000
T393C 4731:669.607 JLINK_ReadReg(FPS20)
T393C 4731:669.640 - 0.033ms returns 0x00000000
T393C 4731:669.682 JLINK_ReadReg(FPS21)
T393C 4731:669.734 - 0.051ms returns 0x00000000
T393C 4731:669.776 JLINK_ReadReg(FPS22)
T393C 4731:669.811 - 0.034ms returns 0x00000000
T393C 4731:669.852 JLINK_ReadReg(FPS23)
T393C 4731:669.886 - 0.033ms returns 0x00000000
T393C 4731:669.927 JLINK_ReadReg(FPS24)
T393C 4731:669.962 - 0.034ms returns 0x00000000
T393C 4731:670.002 JLINK_ReadReg(FPS25)
T393C 4731:670.037 - 0.035ms returns 0x00000000
T393C 4731:670.075 JLINK_ReadReg(FPS26)
T393C 4731:670.106 - 0.031ms returns 0x00000000
T393C 4731:670.144 JLINK_ReadReg(FPS27)
T393C 4731:670.176 - 0.032ms returns 0x00000000
T393C 4731:670.214 JLINK_ReadReg(FPS28)
T393C 4731:670.245 - 0.031ms returns 0x00000000
T393C 4731:670.283 JLINK_ReadReg(FPS29)
T393C 4731:670.314 - 0.030ms returns 0x00000000
T393C 4731:670.353 JLINK_ReadReg(FPS30)
T393C 4731:670.384 - 0.030ms returns 0x00000000
T393C 4731:670.423 JLINK_ReadReg(FPS31)
T393C 4731:670.455 - 0.030ms returns 0x00000000
T3964 4731:678.056 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T3964 4731:678.154   CPU_ReadMem(64 bytes @ 0x00000000)
T3964 4731:680.335    -- Updating C cache (64 bytes @ 0x00000000)
T3964 4731:680.376    -- Read from C cache (4 bytes @ 0x00000000)
T3964 4731:680.414   Data:  00 04 00 20
T3964 4731:680.451 - 2.395ms returns 4 (0x4)
T3964 4731:686.846 JLINK_HasError()
T3964 4731:686.919 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T3964 4731:686.959   CPU_ReadMem(4 bytes @ 0xE0001004)
T3964 4731:688.005   Data:  CE 50 4A 04
T3964 4731:688.046   Debug reg: DWT_CYCCNT
T3964 4731:688.082 - 1.162ms returns 1 (0x1)
T3964 4731:691.576 JLINK_ReadMemEx(0x00027D0C, 0x3C Bytes, Flags = 0x02000000)
T3964 4731:691.653   CPU_ReadMem(128 bytes @ 0x00027D00)
T3964 4731:695.105    -- Updating C cache (128 bytes @ 0x00027D00)
T3964 4731:695.253    -- Read from C cache (60 bytes @ 0x00027D0C)
T3964 4731:695.418   Data:  00 BE 02 20 00 EB 04 40 0E A1 04 F0 43 FF BF F3 ...
T3964 4731:695.478 - 3.902ms returns 60 (0x3C)
T393C 4732:619.209 JLINK_ReadMemEx(0x00027D0C, 0x2 Bytes, Flags = 0x02000000)
T393C 4732:619.288    -- Read from C cache (2 bytes @ 0x00027D0C)
T393C 4732:619.319   Data:  00 BE
T393C 4732:619.350 - 0.142ms returns 2 (0x2)
T393C 4732:619.374 JLINK_HasError()
T393C 4732:619.409 JLINK_WriteReg(R15 (PC), 0x00027D0E)
T393C 4732:619.435 - 0.026ms returns 0
T393C 4732:619.458 JLINK_HasError()
T393C 4732:619.486 JLINK_SetBPEx(Addr = 0x0002B768, Type = 0xFFFFFFF2)
T393C 4732:619.509 - 0.023ms returns 0x00000029
T393C 4732:619.539 JLINK_HasError()
T393C 4732:619.567 JLINK_SetBPEx(Addr = 0x0002B790, Type = 0xFFFFFFF2)
T393C 4732:619.587 - 0.020ms returns 0x0000002A
T393C 4732:619.609 JLINK_HasError()
T393C 4732:619.630 JLINK_SetBPEx(Addr = 0x0002B774, Type = 0xFFFFFFF2)
T393C 4732:619.651 - 0.021ms returns 0x0000002B
T393C 4732:619.673 JLINK_HasError()
T393C 4732:619.695 JLINK_SetBPEx(Addr = 0x0002B76A, Type = 0xFFFFFFF2)
T393C 4732:619.716 - 0.021ms returns 0x0000002C
T393C 4732:619.738 JLINK_HasError()
T393C 4732:619.760 JLINK_SetBPEx(Addr = 0x00029C20, Type = 0xFFFFFFF2)
T393C 4732:619.782 - 0.021ms returns 0x0000002D
T393C 4732:619.803 JLINK_HasError()
T393C 4732:619.825 JLINK_SetBPEx(Addr = 0x00029C00, Type = 0xFFFFFFF2)
T393C 4732:619.860 - 0.034ms returns 0x0000002E
T393C 4732:619.882 JLINK_HasError()
T393C 4732:619.909 JLINK_SetBPEx(Addr = 0x0002F7C4, Type = 0xFFFFFFF2)
T393C 4732:619.930 - 0.021ms returns 0x0000002F
T393C 4732:619.952 JLINK_HasError()
T393C 4732:619.974 JLINK_SetBPEx(Addr = 0x0002F6F0, Type = 0xFFFFFFF2)
T393C 4732:619.995 - 0.021ms returns 0x00000030
T393C 4732:620.017 JLINK_HasError()
T393C 4732:620.044 JLINK_SetBPEx(Addr = 0x00029C6E, Type = 0xFFFFFFF2)
T393C 4732:620.067 - 0.026ms returns 0x00000031
T393C 4732:620.090 JLINK_HasError()
T393C 4732:620.112 JLINK_SetBPEx(Addr = 0x00029C68, Type = 0xFFFFFFF2)
T393C 4732:620.134 - 0.021ms returns 0x00000032
T393C 4732:620.156 JLINK_HasError()
T393C 4732:620.178 JLINK_HasError()
T393C 4732:620.201 JLINK_Go()
T393C 4732:621.336   CPU_WriteMem(4 bytes @ 0x********)
T393C 4732:622.405   CPU_WriteMem(4 bytes @ 0x********)
T393C 4732:623.464   CPU_WriteMem(4 bytes @ 0x********)
T393C 4732:624.394   CPU_WriteMem(4 bytes @ 0x********)
T393C 4732:625.325   CPU_WriteMem(4 bytes @ 0x********)
T393C 4732:626.319   CPU_WriteMem(4 bytes @ 0x********)
T393C 4732:627.298   CPU_ReadMem(4 bytes @ 0xE0001000)
T393C 4732:631.157 - 10.954ms
T393C 4732:732.077 JLINK_HasError()
T393C 4732:732.176 JLINK_IsHalted()
T393C 4732:741.462 - 9.283ms returns TRUE
T393C 4732:741.596 JLINK_HasError()
T393C 4732:741.640 JLINK_Halt()
T393C 4732:741.676 - 0.035ms returns 0x00
T393C 4732:741.717 JLINK_IsHalted()
T393C 4732:741.752 - 0.035ms returns TRUE
T393C 4732:741.802 JLINK_IsHalted()
T393C 4732:741.839 - 0.036ms returns TRUE
T393C 4732:741.875 JLINK_IsHalted()
T393C 4732:741.909 - 0.034ms returns TRUE
T393C 4732:741.954 JLINK_HasError()
T393C 4732:742.000 JLINK_ReadReg(R15 (PC))
T393C 4732:742.041 - 0.041ms returns 0x0002B768
T393C 4732:742.087 JLINK_ReadReg(XPSR)
T393C 4732:742.127 - 0.040ms returns 0x61000000
T393C 4732:742.181 JLINK_HasError()
T393C 4732:742.230 JLINK_ClrBPEx(BPHandle = 0x00000029)
T393C 4732:742.269 - 0.039ms returns 0x00
T393C 4732:742.318 JLINK_HasError()
T393C 4732:742.367 JLINK_ClrBPEx(BPHandle = 0x0000002A)
T393C 4732:742.408 - 0.040ms returns 0x00
T393C 4732:742.456 JLINK_HasError()
T393C 4732:742.507 JLINK_ClrBPEx(BPHandle = 0x0000002B)
T393C 4732:742.547 - 0.040ms returns 0x00
T393C 4732:742.594 JLINK_HasError()
T393C 4732:742.643 JLINK_ClrBPEx(BPHandle = 0x0000002C)
T393C 4732:742.683 - 0.039ms returns 0x00
T393C 4732:742.730 JLINK_HasError()
T393C 4732:742.779 JLINK_ClrBPEx(BPHandle = 0x0000002D)
T393C 4732:742.820 - 0.041ms returns 0x00
T393C 4732:742.868 JLINK_HasError()
T393C 4732:742.924 JLINK_ClrBPEx(BPHandle = 0x0000002E)
T393C 4732:742.965 - 0.041ms returns 0x00
T393C 4732:743.005 JLINK_HasError()
T393C 4732:743.044 JLINK_ClrBPEx(BPHandle = 0x0000002F)
T393C 4732:743.082 - 0.037ms returns 0x00
T393C 4732:743.155 JLINK_HasError()
T393C 4732:743.205 JLINK_ClrBPEx(BPHandle = 0x00000030)
T393C 4732:743.246 - 0.041ms returns 0x00
T393C 4732:743.289 JLINK_HasError()
T393C 4732:743.334 JLINK_ClrBPEx(BPHandle = 0x00000031)
T393C 4732:743.371 - 0.036ms returns 0x00
T393C 4732:743.413 JLINK_HasError()
T393C 4732:743.451 JLINK_ClrBPEx(BPHandle = 0x00000032)
T393C 4732:743.487 - 0.035ms returns 0x00
T393C 4732:743.523 JLINK_HasError()
T393C 4732:743.569 JLINK_HasError()
T393C 4732:743.615 JLINK_ReadMemU32(0xE000ED30, 0x1 Items)
T393C 4732:743.666   CPU_ReadMem(4 bytes @ 0xE000ED30)
T393C 4732:744.960   Data:  02 00 00 00
T393C 4732:745.080 - 1.464ms returns 1 (0x1)
T393C 4732:745.136 JLINK_ReadMemU32(0xE0001028, 0x1 Items)
T393C 4732:745.184   CPU_ReadMem(4 bytes @ 0xE0001028)
T393C 4732:746.354   Data:  00 00 00 00
T393C 4732:746.445   Debug reg: DWT_FUNC[0]
T393C 4732:746.492 - 1.355ms returns 1 (0x1)
T393C 4732:746.550 JLINK_ReadMemU32(0xE0001038, 0x1 Items)
T393C 4732:746.596   CPU_ReadMem(4 bytes @ 0xE0001038)
T393C 4732:747.731   Data:  00 02 00 00
T393C 4732:747.826   Debug reg: DWT_FUNC[1]
T393C 4732:747.871 - 1.320ms returns 1 (0x1)
T393C 4732:747.939 JLINK_ReadMemU32(0xE0001048, 0x1 Items)
T393C 4732:747.992   CPU_ReadMem(4 bytes @ 0xE0001048)
T393C 4732:749.115   Data:  00 00 00 00
T393C 4732:749.197   Debug reg: DWT_FUNC[2]
T393C 4732:749.237 - 1.298ms returns 1 (0x1)
T393C 4732:749.292 JLINK_ReadMemU32(0xE0001058, 0x1 Items)
T393C 4732:749.332   CPU_ReadMem(4 bytes @ 0xE0001058)
T393C 4732:750.491   Data:  00 00 00 00
T393C 4732:750.572   Debug reg: DWT_FUNC[3]
T393C 4732:750.613 - 1.321ms returns 1 (0x1)
T393C 4732:750.751 JLINK_HasError()
T393C 4732:750.797 JLINK_ReadReg(R0)
T393C 4732:750.837 - 0.039ms returns 0x0002B769
T393C 4732:750.867 JLINK_ReadReg(R1)
T393C 4732:750.894 - 0.027ms returns 0x20005C18
T393C 4732:750.923 JLINK_ReadReg(R2)
T393C 4732:750.950 - 0.026ms returns 0x00000000
T393C 4732:750.979 JLINK_ReadReg(R3)
T393C 4732:751.006 - 0.026ms returns 0x000279D1
T393C 4732:751.034 JLINK_ReadReg(R4)
T393C 4732:751.061 - 0.026ms returns 0x000321DC
T393C 4732:751.089 JLINK_ReadReg(R5)
T393C 4732:751.115 - 0.025ms returns 0x000321DC
T393C 4732:751.141 JLINK_ReadReg(R6)
T393C 4732:751.167 - 0.025ms returns 0x10001000
T393C 4732:751.193 JLINK_ReadReg(R7)
T393C 4732:751.219 - 0.025ms returns 0x00000000
T393C 4732:751.246 JLINK_ReadReg(R8)
T393C 4732:751.271 - 0.025ms returns 0x00000000
T393C 4732:751.298 JLINK_ReadReg(R9)
T393C 4732:751.324 - 0.025ms returns 0x00000000
T393C 4732:751.350 JLINK_ReadReg(R10)
T393C 4732:751.375 - 0.025ms returns 0x00000000
T393C 4732:751.402 JLINK_ReadReg(R11)
T393C 4732:751.427 - 0.025ms returns 0x00000000
T393C 4732:751.453 JLINK_ReadReg(R12)
T393C 4732:751.479 - 0.025ms returns 0x00000000
T393C 4732:751.505 JLINK_ReadReg(R13 (SP))
T393C 4732:751.531 - 0.026ms returns 0x20005C18
T393C 4732:751.558 JLINK_ReadReg(R14)
T393C 4732:751.583 - 0.025ms returns 0x000274C1
T393C 4732:751.610 JLINK_ReadReg(R15 (PC))
T393C 4732:751.636 - 0.025ms returns 0x0002B768
T393C 4732:751.663 JLINK_ReadReg(XPSR)
T393C 4732:751.689 - 0.025ms returns 0x61000000
T393C 4732:751.715 JLINK_ReadReg(MSP)
T393C 4732:751.741 - 0.025ms returns 0x20005C18
T393C 4732:751.768 JLINK_ReadReg(PSP)
T393C 4732:751.794 - 0.025ms returns 0x00000000
T393C 4732:751.820 JLINK_ReadReg(CFBP)
T393C 4732:751.845 - 0.024ms returns 0x00000000
T393C 4732:751.873 JLINK_ReadReg(FPSCR)
T393C 4732:766.065 - 14.190ms returns 0x00000000
T393C 4732:766.129 JLINK_ReadReg(FPS0)
T393C 4732:766.156 - 0.026ms returns 0x00000000
T393C 4732:766.179 JLINK_ReadReg(FPS1)
T393C 4732:766.200 - 0.021ms returns 0x00000000
T393C 4732:766.223 JLINK_ReadReg(FPS2)
T393C 4732:766.245 - 0.022ms returns 0x00000000
T393C 4732:766.268 JLINK_ReadReg(FPS3)
T393C 4732:766.290 - 0.021ms returns 0x00000000
T393C 4732:766.313 JLINK_ReadReg(FPS4)
T393C 4732:766.334 - 0.021ms returns 0x00000000
T393C 4732:766.357 JLINK_ReadReg(FPS5)
T393C 4732:766.379 - 0.021ms returns 0x00000000
T393C 4732:766.401 JLINK_ReadReg(FPS6)
T393C 4732:766.430 - 0.028ms returns 0x00000000
T393C 4732:766.453 JLINK_ReadReg(FPS7)
T393C 4732:766.474 - 0.020ms returns 0x00000000
T393C 4732:766.496 JLINK_ReadReg(FPS8)
T393C 4732:766.517 - 0.021ms returns 0x00000000
T393C 4732:766.540 JLINK_ReadReg(FPS9)
T393C 4732:766.561 - 0.021ms returns 0x00000000
T393C 4732:766.584 JLINK_ReadReg(FPS10)
T393C 4732:766.605 - 0.021ms returns 0x00000000
T393C 4732:766.628 JLINK_ReadReg(FPS11)
T393C 4732:766.649 - 0.021ms returns 0x00000000
T393C 4732:766.673 JLINK_ReadReg(FPS12)
T393C 4732:766.694 - 0.021ms returns 0x00000000
T393C 4732:766.717 JLINK_ReadReg(FPS13)
T393C 4732:766.738 - 0.021ms returns 0x00000000
T393C 4732:766.761 JLINK_ReadReg(FPS14)
T393C 4732:766.782 - 0.021ms returns 0x00000000
T393C 4732:766.804 JLINK_ReadReg(FPS15)
T393C 4732:766.826 - 0.021ms returns 0x00000000
T393C 4732:766.848 JLINK_ReadReg(FPS16)
T393C 4732:766.870 - 0.021ms returns 0x00000000
T393C 4732:766.892 JLINK_ReadReg(FPS17)
T393C 4732:766.914 - 0.021ms returns 0x00000000
T393C 4732:766.936 JLINK_ReadReg(FPS18)
T393C 4732:766.958 - 0.021ms returns 0x00000000
T393C 4732:766.980 JLINK_ReadReg(FPS19)
T393C 4732:767.013 - 0.032ms returns 0x00000000
T393C 4732:767.035 JLINK_ReadReg(FPS20)
T393C 4732:767.058 - 0.022ms returns 0x00000000
T393C 4732:767.082 JLINK_ReadReg(FPS21)
T393C 4732:767.105 - 0.022ms returns 0x00000000
T393C 4732:767.128 JLINK_ReadReg(FPS22)
T393C 4732:767.151 - 0.022ms returns 0x00000000
T393C 4732:767.175 JLINK_ReadReg(FPS23)
T393C 4732:767.197 - 0.022ms returns 0x00000000
T393C 4732:767.221 JLINK_ReadReg(FPS24)
T393C 4732:767.244 - 0.023ms returns 0x00000000
T393C 4732:767.268 JLINK_ReadReg(FPS25)
T393C 4732:767.291 - 0.022ms returns 0x00000000
T393C 4732:767.315 JLINK_ReadReg(FPS26)
T393C 4732:767.338 - 0.022ms returns 0x00000000
T393C 4732:767.362 JLINK_ReadReg(FPS27)
T393C 4732:767.385 - 0.022ms returns 0x00000000
T393C 4732:767.408 JLINK_ReadReg(FPS28)
T393C 4732:767.431 - 0.022ms returns 0x00000000
T393C 4732:767.455 JLINK_ReadReg(FPS29)
T393C 4732:767.478 - 0.022ms returns 0x00000000
T393C 4732:767.502 JLINK_ReadReg(FPS30)
T393C 4732:767.524 - 0.022ms returns 0x00000000
T393C 4732:767.548 JLINK_ReadReg(FPS31)
T393C 4732:767.571 - 0.022ms returns 0x00000000
T3964 4732:775.134 JLINK_ReadMemEx(0x00000000, 0x1 Bytes, Flags = 0x02000000)
T3964 4732:775.239   CPU_ReadMem(64 bytes @ 0x00000000)
T3964 4732:777.391    -- Updating C cache (64 bytes @ 0x00000000)
T3964 4732:777.451    -- Read from C cache (1 bytes @ 0x00000000)
T3964 4732:777.498   Data:  00
T3964 4732:777.540 - 2.407ms returns 1 (0x1)
T3964 4732:777.737 JLINK_ReadMemEx(0x00000000, 0x4 Bytes, Flags = 0x02000000)
T3964 4732:777.779    -- Read from C cache (4 bytes @ 0x00000000)
T3964 4732:777.824   Data:  00 04 00 20
T3964 4732:777.865 - 0.128ms returns 4 (0x4)
T3964 4732:784.647 JLINK_HasError()
T3964 4732:784.730 JLINK_ReadMemU32(0xE0001004, 0x1 Items)
T3964 4732:784.775   CPU_ReadMem(4 bytes @ 0xE0001004)
T3964 4732:785.792   Data:  12 E7 4A 04
T3964 4732:785.871   Debug reg: DWT_CYCCNT
T3964 4732:785.910 - 1.179ms returns 1 (0x1)
T3964 4737:070.862 JLINK_HasError()
T3964 4737:078.935 JLINK_Close()
T3964 4737:079.945   CPU_ReadMem(4 bytes @ 0xE000ED90)
T3964 4737:081.134   CPU_ReadMem(4 bytes @ 0xE000ED94)
T3964 4737:082.223    -- Start of preparing flash programming
T3964 4737:082.302    -- Calculating RAM usage
T3964 4737:082.408    -- RAM usage = 9908 Bytes
T3964 4737:082.488    -- Preserving CPU registers
T3964 4737:082.599    -- Preparing target
T3964 4737:082.667    -- Preserving target RAM temporarily used for programming
T3964 4737:266.004    -- Downloading RAMCode
T3964 4737:312.587    -- Preparing RAMCode
T3964 4737:328.677    -- End of preparing flash programming
T3964 4737:328.830    -- Read from flash cache (4096 bytes @ 0x0002B000)
T3964 4737:328.893    -- Programming range 0x0002B000 - 0x0002BFFF (  1 Sector, 4 KB)
T3964 4737:589.881    -- Start of restoring
T3964 4737:589.952    -- Restoring RAMCode
T3964 4737:602.038    -- Restoring target memory
T3964 4737:773.286    -- Restore target
T3964 4737:773.472    -- Restoring CPU registers
T3964 4737:773.578    -- End of restoring
T3964 4737:790.679   CPU_WriteMem(4 bytes @ 0xE0002008)
T3964 4737:790.773   CPU_WriteMem(4 bytes @ 0xE000200C)
T3964 4737:790.793   CPU_WriteMem(4 bytes @ 0xE0002010)
T3964 4737:790.812   CPU_WriteMem(4 bytes @ 0xE0002014)
T3964 4737:790.831   CPU_WriteMem(4 bytes @ 0xE0002018)
T3964 4737:790.850   CPU_WriteMem(4 bytes @ 0xE000201C)
T3964 4737:794.486   CPU_ReadMem(4 bytes @ 0xE0001000)
T3964 4737:825.886 - 746.949ms
T3964 4737:825.940   
T3964 4737:825.952   Closed
